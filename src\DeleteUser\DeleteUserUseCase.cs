﻿using commercetools.Sdk.Api.Models.Customers;
using IT.SharedLibraries.CT.Customers;
using ITF.SharedLibraries.Keycloak;
using System.IdentityModel.Tokens.Jwt;

namespace IT.Microservices.AuthenticationApi.DeleteUser;

public record DeleteUserFailedResponse(string? error = null, string? error_description = null, [property: JsonIgnore] bool? isException = false);


public interface IDeleteUserUseCase
{
    public Task<Result<bool, DeleteUserFailedResponse>> Process(DeleteUserRequest req);
}

public class DeleteUserUseCase(ILogger<DeleteUserUseCase> logger, IConfiguration conf, IKeycloakLoginHttpService keycloakHttpService, ICustomerService custService, SerializerService serializerService, IKeycloakService keycloakService, ILegacyLoginService? legacyLoginService = null) : IDeleteUserUseCase
{
    public async Task<Result<bool, DeleteUserFailedResponse>> Process(DeleteUserRequest req)
    {
        string errorContent = string.Empty;

        if (string.IsNullOrWhiteSpace(req.AccessToken) || string.IsNullOrWhiteSpace(req.Password))
            return new DeleteUserFailedResponse("InvalidRequestData", $"One or more parameters are invalid : req payload received : {(req with { Password = "*******"}).Serialize()}");

        // Validate access token and extract user email
        var tokenIntrospection = await keycloakHttpService.IntrospectTokenAsync(req.AccessToken);
        if (tokenIntrospection.IsFailure)
        {
            errorContent = $"Error while introspecting access token: {tokenIntrospection.Error?.Serialize()}";
            logger.LogError(errorContent);
            return new DeleteUserFailedResponse { error = "TokenIntrospectionFailed", error_description = errorContent };
        }

        if (!tokenIntrospection.Value.active)
        {
            errorContent = "Access token is not active or has expired";
            logger.LogError(errorContent);
            return new DeleteUserFailedResponse { error = "TokenNotActive", error_description = errorContent };
        }

        if (string.IsNullOrWhiteSpace(tokenIntrospection.Value.email))
        {
            errorContent = "Unable to extract email from access token";
            logger.LogError(errorContent);
            return new DeleteUserFailedResponse { error = "EmailNotFoundInToken", error_description = errorContent };
        }
        var authUser = await keycloakHttpService.LoginUser(conf["Keycloak:TokenEndpoint"],
            [
                new("client_id", conf["Keycloak:ClientId"]),new("client_secret", conf["Keycloak:ClientSecret"]),
                        new("username", tokenIntrospection.Value.email),new("password", req.Password),
                        new("grant_type", "password"),new("scope", "openid")
            ]);
        if (authUser.IsFailure)
            return new DeleteUserFailedResponse { error = "LoginUserInKeycloakFailed", error_description = "impossible to login the current user before deleting the account" };
        // delete user in Keycloak
        try
        {
            var userId = new JwtSecurityTokenHandler().ReadJwtToken(authUser.Value.access_token).Claims.First(c => c.Type == "sub").Value;

            var deleteRes = await keycloakService.DeleteUser(userId);
            if (!deleteRes.StatusCode.IsSuccessStatusCode())
            {
                errorContent = $"Error while Deleting the user into Keycloak for email and id : {userId} : {tokenIntrospection.Value.email} with the error : {deleteRes.ErrorText}";
                logger.LogError(errorContent);
                return new DeleteUserFailedResponse { error = "DeletingUserInKeycloakFailed", error_description = errorContent };
            }
        }
        catch (Exception e)
        {
            return new DeleteUserFailedResponse { error = "DeletingUserInKeycloakFailed", error_description = string.IsNullOrEmpty(errorContent) ? $"Exception occurred: {e}" : $"{errorContent} with Exception : {e}", isException = true };
        }
        if (!req.OnlyKeycloak)
        {
            // Get customer from commercetools
            var ctUserExists = await custService.GetByEmail(tokenIntrospection.Value.email);
            if (ctUserExists is null)
            {
                errorContent = $"User with email {tokenIntrospection.Value.email} does not exist in commercetools";
                logger.LogError(errorContent);
                return true;
            }

            // delete user in CT
            ICustomer? deletedCust;
            try
            {
                deletedCust = await custService.DeleteCustomer(ctUserExists);
            }
            catch (Exception e)
            {
                logger.LogError(e, "Exception occurred while deleting the user in CT for email {email} with exception : {e}", tokenIntrospection.Value.email, e.ToString());
                return true;
            }
            if(deletedCust == null) { 
                errorContent = $"Error while Deleting the user into CT for email : {tokenIntrospection.Value.email}";
                logger.LogError(errorContent);
                return true;
            }
        }

        return true;
    }
}
