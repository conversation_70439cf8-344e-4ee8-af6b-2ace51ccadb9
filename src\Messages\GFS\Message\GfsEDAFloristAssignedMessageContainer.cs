﻿using ITF.SharedModels.Group.Enums.Gfs;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace ITF.SharedModels.Messages.GFS.Message
{
    public class GfsEDAFloristAssignedMessageContainer
    {
        [JsonIgnore]
        public static readonly string NameType = "\"Sent Florist Assigned Message\"";

        [JsonProperty("Sent Florist Assigned Message")]
        public GfsFloristAssignedMessage SentFloristAssignedMessage { get; set; } = new();
    }

    public class GfsFloristAssignedMessage
    {
        public DateTime AssignmentDate { get; set; }
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageStatus MessageStatus { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageType MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public int? RelatedMessageId { get; set; }
        public bool Read { get; set; }
    }
}
