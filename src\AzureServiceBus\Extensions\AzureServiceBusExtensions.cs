﻿using ITF.SharedLibraries.AzureServiceBus.Subscriber;
using ITF.SharedLibraries.EnvironmentVariable;
using ITF.SharedLibraries.ExtensionMethods;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using static ITF.SharedLibraries.AzureServiceBus.Subscriber.DelegateHandler;

namespace ITF.SharedLibraries.AzureServiceBus.Extensions
{
    public static class AzureServiceBusExtensions
    {

        public static IServiceCollection AddAzureServiceBusSubscriberHostedService(this IServiceCollection services, IConfiguration config, AzureServiceBusDelegateHandler azureServiceBusActionHandler, string varEnv = "AzureServiceBus")
        {
            azureServiceBusActionHandler?.Invoke(services);

            return services
                .AddSingleton(config.Get<Configuration>(varEnv))
                .AddSingleton<IServiceBusTopicSubscription,ServiceBusTopicSubscription>()
                .AddSingleton<AzureServiceBusSubscriber>() // Allows to access the background service as singleton and stop it
                .AddHostedService<AzureServiceBusSubscriber>();
        }

        public static IServiceCollection AddAzureServiceBusMultipleSubscribersHostedService(this IServiceCollection services, IConfiguration config, string varEnv = "AzureMultiSubscriptionsConfiguration")
        {
            services.Configure<MultiSubscriptionsConfiguration>(config.GetSection(varEnv));

            var azureConfig = config.GetSection(varEnv).Get<MultiSubscriptionsConfiguration>() ?? new();

            // Add named Service Bus clients from config
            services.AddAzureClients(azure =>
            {
                // SAS connection strings
                foreach (var sub in azureConfig.Subscriptions)
                {
                    if (String.IsNullOrWhiteSpace(sub.Name))
                    {
                        throw new Exception($"Azure multiple subscription configuration is wrong: {nameof(sub.Name)} is null or empty");
                    }
                    if (String.IsNullOrWhiteSpace(sub.ConnectionString))
                    {
                        throw new Exception($"Azure multiple subscription configuration is wrong: {nameof(sub.ConnectionString)} is null or empty");
                    }
                    azure.AddServiceBusClient(sub.ConnectionString).WithName(sub.Name);
                }

                // Example for Managed Identity (optional):
                // azure.UseCredential(new DefaultAzureCredential());
                // azure.AddServiceBusClientWithNamespace("my-ns.servicebus.windows.net").WithName("managed");
            });
            services.AddHostedService<ServiceBusSubscriptionsHostedService>();

            return services;
        }

        public static IServiceCollection UseAzureServiceBusSubscriber(this IServiceCollection services, IConfiguration config, AzureServiceBusDelegateHandler azureServiceBusActionHandler, string varEnv = "AzureServiceBus")
        {
            azureServiceBusActionHandler?.Invoke(services);

            var configuration = config.Get<Configuration>(varEnv);

            services.AddAzureServiceBus(configuration);

            // Allows to access the background service as singleton and stop it
            services.AddSingleton<AzureServiceBusSubscriber>();
            return services;
        }
        public static IServiceCollection AddAzureServiceBusBackgroundWorker(this IServiceCollection services)
        {
            return services.AddHostedService(provider => provider.GetService<AzureServiceBusSubscriber>());
        }

        public static IApplicationBuilder AddAzureServiceBusManagementRoutes(this IApplicationBuilder applicationBuilder, string cancelRouteName = "/admin/CancelBackgroundService") =>
            applicationBuilder
                .Map($"{cancelRouteName}", versionApp =>
                    versionApp.UseMiddleware<CancelMiddleware>());

        public static async Task StopSubscribeHandlersAsync(this IServiceProvider serviceProvider)
        {
            var backgroundServices = serviceProvider.GetAllHostedService<AzureServiceBusSubscriber>();
            backgroundServices?.ToList().ForEach(async s =>
            {
                await s?.StopAsync(new System.Threading.CancellationToken());
            });

            await Task.CompletedTask;
        }

        private static IServiceCollection AddAzureServiceBus(this IServiceCollection services, Configuration configuration)
            => services.AddSingleton(configuration)
                        .AddSingleton<IServiceBusTopicSubscription, ServiceBusTopicSubscription>();
    }
}
