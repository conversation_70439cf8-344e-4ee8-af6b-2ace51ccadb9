﻿using commercetools.Base.Client;
using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.Customers;
using commercetools.Sdk.Api.Serialization;
using ITF.SharedLibraries.ExtensionMethods;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace IT.SharedLibraries.CT.Customers;

public interface ICustomerService
{
    public Task<ICustomer?> GetByEmail(string email);
    public Task<ICustomer?> CreateCustomer(ICustomerDraft cust);
    public Task<ICustomer?> UpdateCustomer(ICustomer customer, List<ICustomerUpdateAction> actions);
    public Task<ICustomer?> DeleteCustomer(ICustomer customer);
    public Task<ICustomerToken?> CreateEmailToken(ICustomer customer, int ttlMinutes = 4320);
    public Task<ICustomer?> GetCustomerByEmailToken(string emailToken);
    public Task<ICustomer?> VerifyEmailToken(string tokenValue);
}
public class CustomerService(IClient commerceToolsClient, IConfiguration configuration, ILogger<CustomerService> logger , SerializerService serializerService) : ICustomerService
{
    private readonly string? _projectKey = configuration?.GetSection("Client:ProjectKey").Value;
    private readonly string? _storeKey = configuration?.GetSection("Client:StoreProjectionKey").Value;

    public async Task<ICustomer?> GetByEmail(string email)
    {
        try
        {
            var cust = await commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                .Customers()
                .Get()
                .WithWhere($"email=\"{email}\"")
                .WithExpand("customerGroupAssignments[*].customerGroup")
                .WithExpand("customerGroup")
                .ExecuteAsync();

            if (cust?.Count == 0)
                return null;

            if (!string.IsNullOrWhiteSpace(_storeKey))
                return cust!.Results?.FirstOrDefault(c => c.Stores.Count == 0 || c.Stores.FirstOrDefault(s => s.Key == _storeKey) != null);

            return cust!.Results?.FirstOrDefault();
        }
        catch (BadRequestException ex)
        {
            logger.LogError(ex, "Error while reading customer with email={email}, because of {ex}",email,ex.ToString());
            return null;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while reading customer with email={email} because of {ex}",email,ex);
            return null;
        }
    }

    public async Task<ICustomer?> CreateCustomer(ICustomerDraft cust)
    {
        try
        {
            var customer = await commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                .Customers()
                .Post(cust)
                .WithExpand("customerGroupAssignments[*].customerGroup")
                .WithExpand("customerGroup")
                .ExecuteAsync();
            return customer?.Customer;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while creating customer with email={email} because of {ex}", cust.Email, ex);
            return null;
        }
    }

    public async Task<ICustomer?> UpdateCustomer(ICustomer customer , List<ICustomerUpdateAction> actions)
    {
        CustomerUpdate update = null;
        try
        {
            update = new CustomerUpdate
            {
                Version = customer.Version,
                Actions = actions
            };

            var updatedCustomer = await commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                .Customers()
                .WithId(customer.Id)
                .Post(update)
                .WithExpand("customerGroupAssignments[*].customerGroup")
                .WithExpand("customerGroup")
                .ExecuteAsync();

            return updatedCustomer;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while updating customer with email={email} for payload : {payload} because of {ex}", customer.Email, update?.Serialize(Serializer.SerializerType.CommerceTools,serializerService) , ex);
            return null;
        }
    }
    public async Task<ICustomer?> DeleteCustomer(ICustomer customer)
    {
        try
        {
            var deletedCustomer = await commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                .Customers()
                .WithId(customer.Id)
                .Delete()
                .WithExpand("customerGroupAssignments[*].customerGroup")
                .WithExpand("customerGroup")
                .ExecuteAsync();

            return deletedCustomer;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while deleting customer with email={email} because of {ex}", customer.Email, ex);
            return null;
        }
    }

    public async Task<ICustomerToken?> CreateEmailToken(ICustomer customer, int ttlMinutes = 4320)
    {
        try
        {
            var tokenRequest = new CustomerCreateEmailToken
            {
                Id = customer.Id,
                TtlMinutes = ttlMinutes
            };

            ICustomerToken? token;
            // Check if customer is store-specific (has stores assigned and one of them matches current store)
            bool isStoreSpecific = !string.IsNullOrWhiteSpace(_storeKey) &&
                                   customer.Stores?.Count > 0 &&
                                   customer.Stores.Any(s => s.Key == _storeKey);

            if (isStoreSpecific)
            {
                // Use store-specific endpoint for store-specific customers
                token = await commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .InStoreKeyWithStoreKeyValue(_storeKey)
                    .Customers()
                    .EmailToken()
                    .Post(tokenRequest)
                    .ExecuteAsync();
            }
            else
            {
                // Use global customer endpoint for global customers
                token = await commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Customers()
                    .EmailToken()
                    .Post(tokenRequest)
                    .ExecuteAsync();
            }

            return token;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while creating email token for customer with id={customerId} because of {ex}", customer.Id, ex);
            return null;
        }
    }

    public async Task<ICustomer?> GetCustomerByEmailToken(string emailToken)
    {
        try
        {
            ICustomer? customer = null;

            // Try store-specific endpoint first if store key is configured
            if (!string.IsNullOrWhiteSpace(_storeKey))
            {
                try
                {
                    customer = await commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                        .InStoreKeyWithStoreKeyValue(_storeKey)
                        .Customers()
                        .WithEmailToken(emailToken)
                        .Get()
                        .WithExpand("customerGroupAssignments[*].customerGroup")
                        .WithExpand("customerGroup")
                        .ExecuteAsync();
                }
                catch (NotFoundException)
                {
                    // Token not found in store-specific endpoint, will try global endpoint
                    logger.LogInformation("Email token not found in store-specific endpoint for storeKey={storeKey}, trying global endpoint", _storeKey);
                    customer = null;
                }
            }

            // If not found in store-specific endpoint or no store key, try global endpoint
            if (customer == null)
            {
                try
                {
                    customer = await commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                        .Customers()
                        .WithEmailToken(emailToken)
                        .Get()
                        .WithExpand("customerGroupAssignments[*].customerGroup")
                        .WithExpand("customerGroup")
                        .ExecuteAsync();
                }
                catch (NotFoundException)
                {
                    // Token not found in either endpoint
                    logger.LogInformation("Email token not found in global endpoint");
                    return null;
                }
            }

            return customer;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while getting customer by email token={emailToken} because of {ex}", emailToken, ex);
            return null;
        }
    }

    public async Task<ICustomer?> VerifyEmailToken(string tokenValue)
    {
        try
        {
            var verifyRequest = new CustomerEmailVerify
            {
                TokenValue = tokenValue
            };

            ICustomer? customer = null;

            // Try store-specific endpoint first if store key is configured
            if (!string.IsNullOrWhiteSpace(_storeKey))
            {
                try
                {
                    customer = await commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                        .InStoreKeyWithStoreKeyValue(_storeKey)
                        .Customers()
                        .EmailConfirm()
                        .Post(verifyRequest)
                        .ExecuteAsync();
                }
                catch (NotFoundException)
                {
                    // Token not found in store-specific endpoint, will try global endpoint
                    logger.LogInformation("Email token not found in store-specific endpoint for storeKey={storeKey}, trying global endpoint", _storeKey);
                    customer = null;
                }
                catch (BadRequestException)
                {
                    // Token might be invalid or expired in store context, try global
                    logger.LogInformation("Email token invalid or expired in store-specific endpoint for storeKey={storeKey}, trying global endpoint", _storeKey);
                    customer = null;
                }
            }

            // If not found in store-specific endpoint or no store key, try global endpoint
            if (customer == null)
            {
                customer = await commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Customers()
                    .EmailConfirm()
                    .Post(verifyRequest)
                    .ExecuteAsync();
            }

            return customer;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error while verifying email token={tokenValue} because of {ex}", tokenValue, ex);
            return null;
        }
    }
}
