﻿using Amazon.Runtime.Internal.Endpoints.StandardLibrary;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Messages.GFS.Product;
using ITF.SharedModels.Messages.Italy;
using System.Xml.Linq;

namespace ITF.SharedModels.Messages.Group.Gfs
{
    public enum GfsProductOperation
    {
        Created,
        Updated,
        Deleted
    }

    public static partial class Messages
    {
        public static partial class V1
        {
            public class GroupGFSSavedPublishedProductMessage : BaseMessage<GroupGFSSavedPublishedProductPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey()
                => Payload?.Id + "_" + Payload?.CountryCode + "_" + Payload?.IntercatCode 
                    + "_" + ((Payload?.LastUpdate.HasValue ?? false) ? Payload?.LastUpdate.Value.ToString("aaaaMMddhhmmss") : "nd")
                    + "_" + ((Payload?.CreatedDate.HasValue ?? false) ? Payload?.CreatedDate.Value.ToString("aaaaMMddhhmmss") : "nd")
                    + "_" + ((Payload?.RemovedDate.HasValue ?? false) ? Payload?.RemovedDate.Value.ToString("aaaaMMddhhmmss") : "nd");

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public GroupGFSSavedPublishedProductMessage()
                {
                    this.Payload = new GroupGFSSavedPublishedProductPayload();
                }

                public void SetOperation(GfsProductOperation operation)
                {
                    this.Payload.Operation = operation;
                }

                public static implicit operator GroupGFSSavedPublishedProductMessage(GFSSavedPublishedProductMessage v)
                {
                    GroupGFSSavedPublishedProductMessage gm = new();
                    gm.Payload = new();
                    gm.Payload.Id = v.SavedPublishedProduct.Id;
                    gm.Payload.CountryCode = v.SavedPublishedProduct.CountryCode;
                    gm.Payload.IntercatCode = v.SavedPublishedProduct.IntercatCode;
                    gm.Payload.InternalName = v.SavedPublishedProduct.InternalName;
                    gm.Payload.Name = v.SavedPublishedProduct.Name;
                    gm.Payload.InternalDescription = v.SavedPublishedProduct.InternalDescription;
                    gm.Payload.ShortDescription = v.SavedPublishedProduct.ShortDescription;
                    gm.Payload.LongDescription = v.SavedPublishedProduct.LongDescription;
                    gm.Payload.IsAddOn = v.SavedPublishedProduct.IsAddOn;
                    gm.Payload.IsGeneric = v.SavedPublishedProduct.IsGeneric;
                    gm.Payload.DeliveryDelay = v.SavedPublishedProduct.DeliveryDelay;
                    gm.Payload.LastUpdate = v.SavedPublishedProduct.LastUpdate;
                    gm.Payload.CreatedDate = v.SavedPublishedProduct.CreatedDate;
                    gm.Payload.RemovedDate = v.SavedPublishedProduct.RemovedDate;
                    gm.Payload.ProductType = v.SavedPublishedProduct.ProductType;
                    foreach (var price in v.SavedPublishedProduct.MinPriceImages)
                    {
                        gm.Payload.MinPriceImages.Add(new GroupGFSMedia
                        {
                            Id = price.Id,
                            URL = price.URL,
                            FileSize = price.FileSize,
                            Width = price.Width,
                            Height = price.Height,
                            LastUpdate = price.LastUpdate,
                        });
                    }
                    foreach (var price in v.SavedPublishedProduct.MidPriceImages)
                    {
                        gm.Payload.MidPriceImages.Add(new GroupGFSMedia
                        {
                            Id = price.Id,
                            URL = price.URL,
                            FileSize = price.FileSize,
                            Width = price.Width,
                            Height = price.Height,
                            LastUpdate = price.LastUpdate,
                        });
                    }
                    foreach (var price in v.SavedPublishedProduct.MaxPriceImages)
                    {
                        gm.Payload.MaxPriceImages.Add(new GroupGFSMedia
                        {
                            Id = price.Id,
                            URL = price.URL,
                            FileSize = price.FileSize,
                            Width = price.Width,
                            Height = price.Height,
                            LastUpdate = price.LastUpdate,
                        });
                    }
                    foreach (var availability in v.SavedPublishedProduct.Availabilities)
                    {
                        gm.Payload.Availabilities.Add(new GroupGFSAvailability
                        {
                            EndDate = availability.EndDate,
                            IsTemplate = availability.IsTemplate,
                            SKUMin = availability.SKUMin,
                            SKUMid = availability.SKUMid,
                            SKUMax = availability.SKUMax,
                            MaxPrice = availability.MaxPrice,
                            MidPrice = availability.MidPrice,
                            MinPrice = availability.MinPrice,
                            StartDate = availability.StartDate,
                            LastUpdate = availability.LastUpdate,
                        });
                    }
                    foreach (var peakPeriod in v.SavedPublishedProduct.PeakPeriods)
                    {
                        gm.Payload.PeakPeriods.Add(new GroupGFSAvailability
                        {
                            EndDate = peakPeriod.EndDate,
                            IsTemplate = peakPeriod.IsTemplate,
                            SKUMin = peakPeriod.SKUMin,
                            SKUMid = peakPeriod.SKUMid,
                            SKUMax = peakPeriod.SKUMax,
                            MaxPrice = peakPeriod.MaxPrice,
                            MidPrice = peakPeriod.MidPrice,
                            MinPrice = peakPeriod.MinPrice,
                            StartDate = peakPeriod.StartDate,
                            LastUpdate = peakPeriod.LastUpdate,
                        });
                    }
                    foreach (var pid in v.SavedPublishedProduct.ProductCategories)
                    {
                        gm.Payload.ProductCategories.Add(pid);
                    }
                    return gm;
                }
            }
        }
    }

    public class GroupGFSSavedPublishedProductPayload : LegacyPayload, IEquatable<GroupGFSSavedPublishedProductPayload>
    {
        public GroupGFSSavedPublishedProductPayload()
        {
            MinPriceImages = [];
            MidPriceImages = [];
            MaxPriceImages = [];
            Availabilities = [];
            PeakPeriods = [];
            ProductCategories = [];

        }

        public int? Id { get; set; }
        public string? CountryCode { get; set; }
        public string? IntercatCode { get; set; }
        public string? InternalName { get; set; }
        public string? Name { get; set; }
        public string? InternalDescription { get; set; }
        public string? ShortDescription { get; set; }
        public string? LongDescription { get; set; }
        public List<GroupGFSMedia> MinPriceImages { get; set; }
        public List<GroupGFSMedia> MidPriceImages { get; set; }
        public List<GroupGFSMedia> MaxPriceImages { get; set; }
        public List<GroupGFSAvailability> Availabilities { get; set; }
        public List<GroupGFSAvailability> PeakPeriods { get; set; }
        public List<int> ProductCategories { get; set; }
        public int? ProductType { get; set; }
        public bool? IsAddOn { get; set; }
        public bool? IsGeneric { get; set; }
        public int? DeliveryDelay { get; set; }
        public DateTime? LastUpdate { get; set; }
        public DateTime? CreatedDate { get; set; }
        public DateTime? RemovedDate { get; set; }
        public GfsProductOperation Operation { get; internal set; }

        public bool Equals(GroupGFSSavedPublishedProductPayload parameter)
        {
            return (Id == parameter.Id &&
                    CountryCode == parameter.CountryCode &&
                    IntercatCode == parameter.IntercatCode &&
                    InternalName == parameter.InternalName &&
                    Name == parameter.Name &&
                    InternalDescription == parameter.InternalDescription &&
                    ShortDescription == parameter.ShortDescription &&
                    LongDescription == parameter.LongDescription &&
                    MinPriceImages.Equals(parameter.MinPriceImages) &&
                    MidPriceImages.Equals(parameter.MidPriceImages) &&
                    MaxPriceImages.Equals(parameter.MaxPriceImages) &&
                    Availabilities.Equals(parameter.Availabilities) &&
                    PeakPeriods.Equals(parameter.PeakPeriods) &&
                    ProductCategories.Equals(parameter.ProductCategories) &&
                    ProductType == parameter.ProductType &&
                    IsAddOn == parameter.IsAddOn &&
                    IsGeneric == parameter.IsGeneric &&
                    DeliveryDelay == parameter.DeliveryDelay &&
                    LastUpdate == parameter.LastUpdate &&
                    CreatedDate == parameter.CreatedDate &&
                    RemovedDate == parameter.RemovedDate &&
                    Operation == parameter.Operation);
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GroupGFSSavedPublishedProductPayload);
        }

        public override int GetHashCode() => new
        {
            Id,
            CountryCode,
            IntercatCode,
            InternalName,
            Name,
            InternalDescription,
            ShortDescription,
            LongDescription,
            MinPriceImages,
            MidPriceImages,
            MaxPriceImages,
            Availabilities,
            PeakPeriods,
            ProductCategories,
            ProductType,
            IsAddOn,
            IsGeneric,
            DeliveryDelay,
            LastUpdate,
            CreatedDate,
            RemovedDate,
            Operation
        }.GetHashCode();
    }

    public class GroupGFSMedia
    {
        public int? Id { get; set; }
        public string URL { get; set; }
        public int? FileSize { get; set; }
        public int? Width { get; set; }
        public int? Height { get; set; }
        public DateTime? LastUpdate { get; set; }

        public bool Equals(GroupGFSMedia parameter)
        {
            return (Id == parameter.Id &&
                    URL == parameter.URL &&
                    FileSize == parameter.FileSize &&
                    Width == parameter.Width &&
                    Height == parameter.Height &&
                    LastUpdate == parameter.LastUpdate);
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GroupGFSMedia);
        }
    }

    public class GroupGFSAvailability
    {
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool? IsTemplate { get; set; }
        public string? SKUMin { get; set; }
        public string? SKUMid { get; set; }
        public string? SKUMax { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MidPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public DateTime? LastUpdate { get; set; }

        public bool Equals(GroupGFSAvailability parameter)
        {
            return (StartDate == parameter.StartDate &&
                    EndDate == parameter.EndDate &&
                    IsTemplate == parameter.IsTemplate &&
                    SKUMin == parameter.SKUMin &&
                    SKUMid == parameter.SKUMid &&
                    SKUMax == parameter.SKUMax &&
                    MinPrice == parameter.MinPrice &&
                    MidPrice == parameter.MidPrice &&
                    MaxPrice == parameter.MaxPrice &&
                    LastUpdate == parameter.LastUpdate);
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GroupGFSAvailability);
        }
    }
}
