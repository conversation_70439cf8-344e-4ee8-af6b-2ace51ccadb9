﻿using commercetools.Sdk.Api.Models.CustomerGroups;
using commercetools.Sdk.Api.Models.Customers;
using commercetools.Sdk.Api.Models.Stores;
using FS.Keycloak.RestApiClient.Model;
using IT.SharedLibraries.CT.Customers;
using ITF.SharedLibraries.Keycloak;
using static ITF.SharedModels.Messages.Group.Customer.Messages.V1;

namespace IT.Microservices.AuthenticationApi.CreateUser;

public interface ICreateUserUseCase
{
    public Task<Result<CreateUserResponse, CreateUserFailedResponse>> Process(CreateUserRequest req);
    public Task<Result<ValidateUserAccountResponse, ValidateUserAccountFailedResponse>> Process(ValidateUserAccountRequest req);
}

// Context records for functional pipeline
public record CreateUserContext(
    CreateUserRequest Request,
    ICustomer? Customer = null,
    string? KeycloakUserId = null
);

public record ValidateUserAccountContext(
    ValidateUserAccountRequest Request,
    ICustomer? Customer = null,
    UserRepresentation? KeycloakUser = null
);

public class CreateUserUseCase(ILogger<CreateUserUseCase> logger, IConfiguration conf, IKeycloakLoginHttpService keycloakHttpService, ICustomerService custService, SerializerService serializerService, IKeycloakService keycloakService, ILegacyLoginService? legacyLoginService = null , IKafkaPublisher? kafkaPublisher = null) : ICreateUserUseCase
{
    private readonly string? _storeKey = conf?.GetSection("Client:StoreProjectionKey").Value;

    // Helper methods for error creation
    private static CreateUserFailedResponse CreateError(string error, string description, bool isException = false) =>
        new(error, description, isException);

    private static ValidateUserAccountFailedResponse CreateValidationError(string error, string description, bool isException = false) =>
        new(error, description, isException);

    // Pipeline methods for CreateUser flow
    private static string GenerateRedirectUrl(string baseUrl, string token , string email) => $"{baseUrl}{(baseUrl.Contains('?') ? "&" : "?")}userId={email}&code={token}";

    private static Result<CreateUserContext, CreateUserFailedResponse> ValidateCreateUserRequest(CreateUserContext context) =>
        string.IsNullOrWhiteSpace(context.Request.Email) || string.IsNullOrWhiteSpace(context.Request.Password) || string.IsNullOrWhiteSpace(context.Request.RedirectUrl)
            ? CreateError("InvalidRequestData", $"One or more parameters are invalid : req payload received : {(context.Request with { Password = "******"}).Serialize()}")
            : context with { Request = context.Request with { Email = context.Request.Email.Trim().ToLower() } };

    private async Task<Result<CreateUserContext, CreateUserFailedResponse>> CheckKeycloakUserNotExists(CreateUserContext context)
    {
        UserRepresentation? user = null;
        var tryRes = await Result.Try(async () => { user = await keycloakService.GetKeycloackUserByUsernameOrNullAsync(context.Request.Email); }, ex => ex?.ToString());

        if (tryRes.IsFailure)
        {
            var err = $"Exception occurred while checking Keycloak user existence for {context.Request.Email}: {tryRes.Error}";
            logger.LogError("{Message}", err);
            return CreateError("KeycloakUserCheckFailed", err, true);
        }

        if (user is not null)
        {
            var errorContent = $"User with email {context.Request.Email} already exists in Keycloak";
            logger.LogError("{Message}", errorContent);
            return CreateError("UserAlreadyExist", errorContent);
        }

        return context;
    }

    private async Task<Result<CreateUserContext, CreateUserFailedResponse>> CreateKeycloakUser(CreateUserContext context)
    {
        var keycloakRes = await keycloakService.CreateUser(new UserRepresentation
        {
            Enabled = true,
            EmailVerified = false,
            Username = context.Request.Email,
            Email = context.Request.Email,
            Credentials = [new CredentialRepresentation { Type = "password", Temporary = false, Value = context.Request.Password }],
            RequiredActions = ["VERIFY_EMAIL"]
        });

        if (!keycloakRes.StatusCode.IsSuccessStatusCode())
        {
            var errorContent = $"Error while creating the user into Keycloak for email : {context.Request.Email} with the error : {keycloakRes.ErrorText}";
            logger.LogError("{Message}", errorContent);
            return CreateError("CreationInKeycloakFailed", errorContent);
        }

        // Try to retrieve created user id for potential rollback later
        string? userId = null;
        var idRes = await Result.Try(async () => { userId = await keycloakService.GetUserIdByUsername(context.Request.Email); }, ex => ex?.ToString());

        if (idRes.IsFailure || string.IsNullOrWhiteSpace(userId))
        {
            logger.LogWarning("Could not retrieve Keycloak user id after creation for email {Email}. Rollback will be best-effort only. Error={Error}", context.Request.Email, idRes.IsFailure ? idRes.Error : "<empty-id>");
            return context;
        }

        return context with { KeycloakUserId = userId };
    }

    private async Task<Result<CreateUserContext, CreateUserFailedResponse>> GetOrCreateCustomerInCT(CreateUserContext context)
    {
        var ctUserExists = await custService.GetByEmail(context.Request.Email);

        if (ctUserExists is null)
        {
            var customer = await CreateNewCustomer(context.Request);
            return customer.IsSuccess
                ? context with { Customer = customer.Value }
                : customer.Error;
        }
        else
        {
            var customer = await UpdateExistingCustomer(ctUserExists, context.Request);
            return customer.IsSuccess
                ?context with { Customer = customer.Value }
                : customer.Error;
        }
    }

    private async Task<Result<ICustomer, CreateUserFailedResponse>> CreateNewCustomer(CreateUserRequest req)
    {
        var ctUserDraft = new CustomerDraft
        {
            Email = req.Email,
            FirstName = req.FirstName ?? "",
            LastName = req.LastName ?? "",
            Addresses = [],
            IsEmailVerified = false,
            AuthenticationMode = IAuthenticationMode.ExternalAuth,
            Stores = [new StoreResourceIdentifier() { Key = _storeKey }],
            CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey },
            CustomerGroupAssignments = [new CustomerGroupAssignmentDraft() { CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey } }],
        };

        var ctRes = await custService.CreateCustomer(ctUserDraft);
        if (ctRes is null)
        {
            var errorContent = $"Error while creating the user with payload draft : {ctUserDraft.Serialize(Serializer.SerializerType.CommerceTools, serializerService)} in CT from request into CT for email : {req.Email}";
            logger.LogError("{Message}", errorContent);
            return CreateError("CreationInCTFailed", errorContent);
        }

        return Result.Success<ICustomer, CreateUserFailedResponse>(ctRes);
    }

    private async Task<Result<ICustomer, CreateUserFailedResponse>> UpdateExistingCustomer(ICustomer ctUserExists, CreateUserRequest req)
    {
        logger.LogWarning("User with data from CT : {Payload} with email {Email} already exists in CT, update process to change customerGroups",
            ctUserExists.Serialize(Serializer.SerializerType.CommerceTools, serializerService), req.Email);

        var customerUpdateActions = DetermineCustomerUpdateActions(ctUserExists , req);

        if (customerUpdateActions.Count == 0)
        {
            logger.LogInformation("User with email {Email} already exists in CT and no update actions are needed", req.Email);
            return Result.Success<ICustomer, CreateUserFailedResponse>(ctUserExists);
        }

        var updatedCust = await custService.UpdateCustomer(ctUserExists, customerUpdateActions);
        if (updatedCust == null)
        {
            var errorContent = $"Error while updating the user : {ctUserExists.Serialize(Serializer.SerializerType.CommerceTools, serializerService)} with payload actions update : {customerUpdateActions.Serialize(Serializer.SerializerType.CommerceTools, serializerService)} in CT from request into CT for email : {req.Email}";
            logger.LogError(errorContent);
            return CreateError("UpdateInCTFailed", errorContent);
        }

        return Result.Success<ICustomer, CreateUserFailedResponse>(updatedCust);
    }

    private List<ICustomerUpdateAction> DetermineCustomerUpdateActions(ICustomer ctUserExists , CreateUserRequest req)
    {
        var customerUpdateActions = new List<ICustomerUpdateAction>();

        // if no stores are assigned to the user, we add the store of the current country
        if (ctUserExists.Stores is null || ctUserExists.Stores.Count == 0)
        {
            logger.LogWarning("User with email {Email} already exists in CT but has no stores, adding store {StoreKey}", ctUserExists.Email, _storeKey);
            customerUpdateActions.Add(new CustomerAddStoreAction() { Store = new StoreResourceIdentifier() { Key = _storeKey } });
        }
        else if (ctUserExists.Stores.All(s => s.Key != _storeKey))
        {
            logger.LogWarning("User with email {Email} already exists in CT but does not have store {StoreKey} current stores : {Stores} , adding it",
                ctUserExists.Email, _storeKey, ctUserExists.Stores?.Serialize(Serializer.SerializerType.CommerceTools, serializerService));
            customerUpdateActions.Add(new CustomerAddStoreAction() { Store = new StoreResourceIdentifier() { Key = _storeKey } });
        }

        // Customer group management
        AddCustomerGroupUpdateActions(ctUserExists, customerUpdateActions);

        // Update first and last name if provided in the request and different from existing ones in commerceTools
        if (!string.IsNullOrWhiteSpace(req.LastName) && req.LastName?.Trim() != ctUserExists.LastName?.Trim())
            customerUpdateActions.Add(new CustomerSetLastNameAction() { LastName = req.LastName });
        if (!string.IsNullOrWhiteSpace(req.FirstName) && req.FirstName?.Trim() != ctUserExists.FirstName?.Trim())
            customerUpdateActions.Add(new CustomerSetFirstNameAction() { FirstName = req.FirstName });

        return customerUpdateActions;
    }

    private void AddCustomerGroupUpdateActions(ICustomer ctUserExists, List<ICustomerUpdateAction> customerUpdateActions)
    {
        // if the user is not in the registered customer group, we add it
        if (ctUserExists.CustomerGroupAssignments is null ||
            ctUserExists.CustomerGroupAssignments.All(cga => cga.CustomerGroup?.Obj?.Key != CustomerCommonValues.RegisteredCustomerGroupKey))
        {
            logger.LogWarning("User with email {Email} already exists in CT but does not have customer group 'registered', adding it", ctUserExists.Email);
            customerUpdateActions.Add(new CustomerAddCustomerGroupAssignmentAction
            {
                CustomerGroupAssignment = new CustomerGroupAssignmentDraft()
                {
                    CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey }
                }
            });
        }

        // if the user is in the anonymous customer group, we remove it
        if (ctUserExists.CustomerGroupAssignments is not null &&
            ctUserExists.CustomerGroupAssignments.Any(cga => cga.CustomerGroup?.Obj?.Key == CustomerCommonValues.AnonymousCustomerGroupKey))
        {
            logger.LogWarning("User with email {Email} already exists in CT but has customer group 'anonymous', removing it", ctUserExists.Email);
            customerUpdateActions.Add(new CustomerRemoveCustomerGroupAssignmentAction
            {
                CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.AnonymousCustomerGroupKey }
            });
        }

        // Handle main customer group
        if (ctUserExists.CustomerGroup == null)
        {
            logger.LogWarning("User with email {Email} already exists in CT but has no customer group, set it with 'registered' group", ctUserExists.Email);
            customerUpdateActions.Add(new CustomerSetCustomerGroupAction
            {
                CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey }
            });
        }
        else if (ctUserExists.CustomerGroup?.Obj?.Key == CustomerCommonValues.AnonymousCustomerGroupKey)
        {
            logger.LogWarning("User with email {Email} already exists in CT but has customer group '{CustomerGroup}', replacing it with 'registered'",
                ctUserExists.Email, ctUserExists.CustomerGroup?.Obj?.Key);
            customerUpdateActions.Add(new CustomerSetCustomerGroupAction
            {
                CustomerGroup = new CustomerGroupResourceIdentifier() { Key = CustomerCommonValues.RegisteredCustomerGroupKey }
            });
        }
    }

    private async Task<Result<CreateUserResponse, CreateUserFailedResponse>> GenerateEmailTokenAndRedirectUrl(CreateUserContext context)
    {
        var customer = context.Customer!;
        var redirectUrl = context.Request.RedirectUrl;

        var emailTokenResult = await custService.CreateEmailToken(customer);
        if (emailTokenResult == null || string.IsNullOrEmpty(emailTokenResult.Value))
        {
            var errorContent = $"Error while generating email token for customer : {customer.Id} with email : {context.Request.Email}";
            logger.LogError("{Message}", errorContent);
            return CreateError("EmailTokenGenerationFailed", errorContent);
        }

        var finalRedirectUrl = GenerateRedirectUrl(redirectUrl, emailTokenResult.Value , context.Request.Email);
        logger.LogInformation("Email token generated for customer: {CustomerId}, redirect URL: {RedirectUrl}", customer.Id, finalRedirectUrl);

        return Result.Success<CreateUserResponse, CreateUserFailedResponse>(new CreateUserResponse(true, finalRedirectUrl));
    }

    private async Task TryRollbackKeycloakUser(CreateUserContext context)
    {
        if (string.IsNullOrWhiteSpace(context.KeycloakUserId))
        {
            logger.LogWarning("Rollback skipped: no Keycloak user id for email {Email}", context.Request.Email);
            return;
        }

        FS.Keycloak.RestApiClient.Client.ApiResponse<object>? delResponse = null;
        var tryDel = await Result.Try(async () => { delResponse = await keycloakService.DeleteUser(context.KeycloakUserId!); }, ex => ex?.ToString());
        if (tryDel.IsFailure)
        {
            logger.LogError("Rollback failed: error deleting Keycloak user {UserId} for email {Email} | {Error}", context.KeycloakUserId, context.Request.Email, tryDel.Error);
            return;
        }

        if (delResponse is null || !delResponse.StatusCode.IsSuccessStatusCode())
        {
            logger.LogWarning("Rollback could not delete Keycloak user {UserId} for email {Email}. StatusCode={StatusCode}", context.KeycloakUserId, context.Request.Email, delResponse?.StatusCode);
        }
        else
        {
            logger.LogInformation("Rollback succeeded: deleted Keycloak user {UserId} for email {Email}", context.KeycloakUserId, context.Request.Email);
        }
    }

    private async Task<Result<CreateUserResponse, CreateUserFailedResponse>> RunTransactionCreateorUpdateCustomerInCTAndGenerateEmailTokenWithRollbackForKeycloak(CreateUserContext context)
    {
        var result = await Result.Success<CreateUserContext, CreateUserFailedResponse>(context)
            .Bind(GetOrCreateCustomerInCT)
            .Bind(GenerateEmailTokenAndRedirectUrl);

        if (result.IsSuccess)
        {
            logger.LogInformation("User creation process completed successfully for email {Email}", context.Request.Email);
            await SendKafkaMessage(context , result.Value);
        }
        else
        {
            logger.LogError("User creation process failed for email {Email} with error: {Error}", context.Request.Email, result.Error);
            await TryRollbackKeycloakUser(context);
        }

        return result;
    }

    private async Task SendKafkaMessage(CreateUserContext context , CreateUserResponse result)
    {
        if(context.Customer is null || kafkaPublisher is null)
        {
            logger.LogWarning("Kafka message not sent: missing customer information or Kafka publisher not configured for email {Email}", context.Request.Email);
            return;
        }
        // Send Kafka message to email reactor microservice
        await kafkaPublisher.PublishAsync(new CreatedAccountRequestedMessage { Payload = new CreatedAccountRequestedPayload { CustomerEmail = context.Customer.Email, CustomerFirstName = context.Customer.FirstName, CustomerLastName = context.Customer.LastName, RedirectUrl = result.RedirectUrl } } ,
            conf.Get<ITF.SharedLibraries.Kafka.Configuration>("Kafka").KafkaProducerTopicName , context.Customer.Id);
    }

    public async Task<Result<CreateUserResponse, CreateUserFailedResponse>> Process(CreateUserRequest req) =>
        await Result.Success<CreateUserContext, CreateUserFailedResponse>(new CreateUserContext(req))
            .Bind(ValidateCreateUserRequest)
            .Bind(CheckKeycloakUserNotExists)
            .Bind(CreateKeycloakUser)
            .Bind(RunTransactionCreateorUpdateCustomerInCTAndGenerateEmailTokenWithRollbackForKeycloak);

    // Pipeline methods for ValidateUserAccount flow
    private static Result<ValidateUserAccountContext, ValidateUserAccountFailedResponse> ValidateAccountRequest(ValidateUserAccountContext context) =>
        string.IsNullOrWhiteSpace(context.Request.EmailToken)
            ? CreateValidationError("InvalidRequestData", "Email token is required")
            : context;

    private async Task<Result<ValidateUserAccountContext, ValidateUserAccountFailedResponse>> GetCustomerByEmailToken(ValidateUserAccountContext context)
    {
        ICustomer? customer = null;
        var tryRes = await Result.Try(async () => { customer = await custService.GetCustomerByEmailToken(context.Request.EmailToken); }, ex => ex?.ToString());

        if (tryRes.IsFailure)
        {
            logger.LogError("Exception getting customer by email token | {Error}", tryRes.Error);
            return CreateValidationError("ValidationError", "An error occurred while validating your account. Please try again.", true);
        }

        if (customer is null)
        {
            logger.LogWarning("Get User from Email token failed for token");
            return CreateValidationError("GetUserInCTFromEmailTokenFailed", "Impossible to find the user in CommerceTools with this EmailToken");
        }

        return context with { Customer = customer };
    }

    private async Task<Result<ValidateUserAccountContext, ValidateUserAccountFailedResponse>> GetKeycloakUser(ValidateUserAccountContext context)
    {
        UserRepresentation? keycloakUser = null;
        var tryRes = await Result.Try(async () => { keycloakUser = await keycloakService.GetKeycloackUserByUsernameOrNullAsync(context.Customer!.Email); }, ex => ex?.ToString());

        if (tryRes.IsFailure)
        {
            logger.LogError("Error getting Keycloak user for email: {Email} | {Error}", context.Customer!.Email, tryRes.Error);
            return CreateValidationError("ValidationError", "An error occurred while validating your account. Please try again.", true);
        }

        if (keycloakUser is null)
        {
            logger.LogError("Keycloak user not found for email: {Email}", context.Customer!.Email);
            return CreateValidationError("UserNotFoundInKeycloak", "User not found in authentication system");
        }

        return context with { KeycloakUser = keycloakUser };
    }

    private async Task<Result<ValidateUserAccountContext, ValidateUserAccountFailedResponse>> VerifyEmailToken(ValidateUserAccountContext context)
    {
        ICustomer? verifiedInCt = null;
        var tryRes = await Result.Try(async () => { verifiedInCt = await custService.VerifyEmailToken(context.Request.EmailToken); }, ex => ex?.ToString());

        if (tryRes.IsFailure)
        {
            logger.LogError("Error verifying email token | {Error}", tryRes.Error);
            return CreateValidationError("ValidationError", "An error occurred while validating your account. Please try again.", true);
        }

        if (verifiedInCt is null)
        {
            logger.LogWarning("Email token verification failed");
            return CreateValidationError("InvalidToken", "The email verification token is invalid or has expired");
        }

        logger.LogInformation("Email token verified successfully for customer: {CustomerId}", context.Customer!.Id);
        return context;
    }

    private async Task<Result<ValidateUserAccountResponse, ValidateUserAccountFailedResponse>> UpdateKeycloakEmailVerification(ValidateUserAccountContext context)
    {
        FS.Keycloak.RestApiClient.Client.ApiResponse<object>? response = null;
        var tryRes = await Result.Try(async () => { response = await keycloakService.SetEmailVerified(context.KeycloakUser!); }, ex => ex?.ToString());

        if (tryRes.IsFailure)
        {
            logger.LogError("Error updating Keycloak user for email: {Email} | {Error}", context.Customer!.Email, tryRes.Error);
            return CreateValidationError("KeycloakError", "Failed to update user verification status", true);
        }

        if (response is null || !response.StatusCode.IsSuccessStatusCode())
        {
            logger.LogError("Failed to update email verification status in Keycloak for user: {UserId}, StatusCode: {StatusCode}", context.KeycloakUser!.Id, response?.StatusCode);
            return CreateValidationError("KeycloakUpdateFailed", "Failed to update user verification status");
        }

        logger.LogInformation("Successfully updated email verification status in Keycloak for user: {UserId}", context.KeycloakUser!.Id);
        return new ValidateUserAccountResponse(true, "Email address verified successfully. Your account is now active.");
    }

    public async Task<Result<ValidateUserAccountResponse, ValidateUserAccountFailedResponse>> Process(ValidateUserAccountRequest req) =>
        await Result.Success<ValidateUserAccountContext, ValidateUserAccountFailedResponse>(new ValidateUserAccountContext(req))
            .Bind(ValidateAccountRequest)
            .Bind(GetCustomerByEmailToken)
            .Bind(GetKeycloakUser)
            .Bind(VerifyEmailToken)
            .Bind(UpdateKeycloakEmailVerification);
}
