﻿using ITF.SharedModels.Group.Enums.Gfs;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace ITF.SharedModels.Messages.GFS.Message
{
    public class GfsEDAQueryMessageContainer
    {
        [JsonIgnore]
        public static readonly string NameType = "\"Query Sent for Order\"";

        [JsonProperty("Query Sent for Order")]
        public GfsQueryOrderMessage QuerySentForOrder { get; set; } = new();
    }

    public class GfsQueryOrderMessage
    {
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageStatus MessageStatus { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageType MessageType { get; set; }
        public string Subject { get; set; } = default!;
        public DateTime? ApplicableFrom { get; set; }
        public DateTime? ApplicableTo { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public bool Read { get; set; }
        public string Text { get; set; } = default!;
        public int QueryMessageType { get; set; }
        public List<GfsQueryField> QueryFields { get; set; } = new();

        public string AttachedUrl { get; set; }
        public bool WaitingForResponse { get; set; }
        public int? RelatedMessageId { get; set; }
    }

    public class GfsQueryField
    {
        public enum QueryFieldNameIds
        {
            ItemPrice = 1,
            DeliveryDate = 10,
            TotalOrderValue = 47,
            ExtraDeliveryFee = 48
        }
        public int Field { get; set; } = default!;
        public int ItemNumber { get; set; } = default!;
        public string Value { get; set; } = default!;
    }
}
