steps:
  - task: He<PERSON><PERSON><PERSON><PERSON>r@1
    displayName: 'Helm: Install agent on VM' 
    inputs:
      helmVersionToInstall: '$(helmVersion)'

  - task: AzureCLI@2
    displayName: 'Helm: Add repo'
    inputs:
      azureSubscription: '$(Azure.ServiceConnection)'
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        export HELM_EXPERIMENTAL_OCI=1
        ACR_NAME=$(HelmChartRepository)
        ACR_ClientID=$(HelmChartRepositoryClientID)
        ACR_ClientSecret=$(HelmChartRepositoryClientSecret)
        helm registry login $ACR_NAME.azurecr.io --username $ACR_ClientID --password $ACR_ClientSecret

  - task: AzureCLI@2
    displayName: 'Helm: Extract chart'
    inputs:
      azureSubscription: '$(Azure.ServiceConnection)'
      scriptType: 'bash'
      scriptLocation: 'inlineScript'
      inlineScript: |
        #Export the chart 
        helm pull oci://$(HelmChartRepository).azurecr.io/helm/itauthenticationapi --version $(helmChartVersion) --destination $(build.stagingdirectory)

  - task: HelmDeploy@0
    displayName: Deploy helm release
    inputs:
      connectionType: 'Azure Resource Manager'
      azureSubscription: '$(Azure.ServiceConnection)'
      azureResourceGroup: '$(Azure.resourceGroup)'
      kubernetesCluster: '$(Azure.kubernetesClusterName)'
      namespace: '$(K8S.Namespace)'
      command: 'upgrade'
      chartType: 'FilePath'
      chartPath: '$(build.stagingdirectory)/$(helmChartName)-$(helmChartVersion).tgz'
      chartVersion: '$(helmChartVersion)'
      releaseName: '$(helmReleaseName)'
      overrideValues: 'image.tag=$(commitID),image.repository=$(containerFullPath)'
      valueFile: '$(chartValuesFile)'
      arguments: '--create-namespace --version $(helmChartVersion) --wait --atomic'
      # arguments: '--create-namespace --version $(helmChartVersion) --wait'