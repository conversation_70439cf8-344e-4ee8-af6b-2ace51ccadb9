﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums.Gfs;
using ITF.SharedModels.Messages.GFS.Message;
using ITF.SharedModels.Messages.Italy;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace ITF.SharedModels.Messages.Group.Gfs
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class GroupEDAGeneralMessageSentMessage : BaseMessage<GroupEDAGeneralMessageSentPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey() => Guid.NewGuid().ToString();

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public GroupEDAGeneralMessageSentMessage()
                {
                    this.Payload = new GroupEDAGeneralMessageSentPayload();
                }

                public static implicit operator GroupEDAGeneralMessageSentMessage(GfsEDAGeneralMessageContainer v)
                {
                    GroupEDAGeneralMessageSentMessage msg = new();
                    msg.Payload = new();
                    msg.Payload.IsPersonal = v.GeneralMessageSent.IsPersonal;
                    msg.Payload.Subject = v.GeneralMessageSent.Subject;
                    msg.Payload.Text = v.GeneralMessageSent.Text;
                    msg.Payload.ApplicableFrom = v.GeneralMessageSent.ApplicableFrom;
                    msg.Payload.ApplicableTo = v.GeneralMessageSent.ApplicableTo;
                    msg.Payload.Id = v.GeneralMessageSent.Id;
                    msg.Payload.GFSgateNumber = v.GeneralMessageSent.GFSgateNumber;
                    msg.Payload.MessageStatus = v.GeneralMessageSent.MessageStatus;
                    msg.Payload.MessageType = v.GeneralMessageSent.MessageType;
                    msg.Payload.CreatedDate = v.GeneralMessageSent.CreatedDate;
                    msg.Payload.ModifiedDate = v.GeneralMessageSent.ModifiedDate;
                    msg.Payload.ConfirmedDate = v.GeneralMessageSent.ConfirmedDate;
                    msg.Payload.FromUnitID = v.GeneralMessageSent.FromUnitID;
                    msg.Payload.FromUnitMessageID = v.GeneralMessageSent.FromUnitMessageID;
                    msg.Payload.ToUnitID = v.GeneralMessageSent.ToUnitID;
                    msg.Payload.ToUnitMessageID = v.GeneralMessageSent.ToUnitMessageID;
                    msg.Payload.Operator = v.GeneralMessageSent.Operator;
                    msg.Payload.Priority = v.GeneralMessageSent.Priority;
                    msg.Payload.RelatedMessageId = v.GeneralMessageSent.RelatedMessageId;
                    msg.Payload.Read = v.GeneralMessageSent.Read;
                    return msg;
                }
            }
        }
    }

    public class GroupEDAGeneralMessageSentPayload : LegacyPayload, IEquatable<GroupEDAGeneralMessageSentPayload>
    {
        public bool IsPersonal { get; set; }
        public string Subject { get; set; } = default!;
        public string Text { get; set; } = default!;
        public DateTime? ApplicableFrom { get; set; }
        public DateTime? ApplicableTo { get; set; }
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageStatus MessageStatus { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageType MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public int? RelatedMessageId { get; set; }
        public bool Read { get; set; }

        public bool Equals(GroupEDAGeneralMessageSentPayload parameter)
        {
            return (IsPersonal == parameter.IsPersonal &&
                    Subject == parameter.Subject &&
                    Text == parameter.Text &&
                    ApplicableFrom.Equals(parameter.ApplicableFrom) &&
                    ApplicableTo.Equals(parameter.ApplicableTo) &&
                    Id == parameter.Id &&
                    GFSgateNumber == parameter.GFSgateNumber &&
                    MessageStatus == parameter.MessageStatus &&
                    MessageType.Equals(parameter.MessageType) &&
                    CreatedDate.Equals(parameter.CreatedDate) &&
                    ModifiedDate.Equals(parameter.ModifiedDate) &&
                    ConfirmedDate.Equals(parameter.ConfirmedDate) &&
                    FromUnitID.Equals(parameter.FromUnitID) &&
                    FromUnitMessageID.Equals(parameter.FromUnitMessageID) &&
                    ToUnitID == parameter.ToUnitID &&
                    ToUnitMessageID == parameter.ToUnitMessageID &&
                    Operator == parameter.Operator &&
                    Priority == parameter.Priority &&
                    RelatedMessageId == parameter.RelatedMessageId &&
                    Read == parameter.Read);
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GroupEDAGeneralMessageSentPayload);
        }

        public override int GetHashCode() => new
        {
            IsPersonal,
            Subject,
            Text,
            ApplicableFrom,
            ApplicableTo,
            Id,
            GFSgateNumber,
            MessageStatus,
            MessageType,
            CreatedDate,
            ModifiedDate,
            ConfirmedDate,
            FromUnitID,
            FromUnitMessageID,
            ToUnitID,
            ToUnitMessageID,
            Operator,
            Priority,
            RelatedMessageId,
            Read
        }.GetHashCode();
    }
}
