# ITF.SharedLibraries

This library aims to provide guidances and best practices in the same place about various tools used by other Microservices. It supposed to hold all external references that should be reused by more than one MS.

## Install

We can install the Nuget package from private feed using the bash

```bash
Install-Package ITF.SharedLibraries
```
or
editing the .csproj file of any projet, example :
```csharp
  <ItemGroup>
    <PackageReference Include="ITF.SharedLibraries" Version="1.2.0" />
  </ItemGroup>
```

## Usage / examples

### Http client
When we need an Http Client with resiliency, the client provides generic methods. Methods specifics to a MS should be implemented in the MS itself.
```csharp
// Declaration of specific interface
public interface IMyHttpService : IHttpClient
{
    Task<List<string>> GetSpecificInformations();
}

// Implementation of the previous interface
public class MyHttpService : HttpClient, IMyHttpService
{
    public Task<List<string>> GetSpecificInformations()
    {
        // ... do something
    }
}
```

```csharp
// Declaration in Startup.cs
public void ConfigureServices(IServiceCollection services)
{
  services.AddHttpClientWithPolicy<IMyHttpService, MyHttpService>(Configuration, "myEndpoint");
}
```
Note that several prototypes are available for extension methods, especially for retry policy fine grained settings.

```csharp
// Service using the http service with DI
public class AnyService 
{
  private readonly IMyHttpService _myHttpService;

  public AnyService(IMyHttpService myHttpService)
  {
      _myHttpService = myHttpService;
  }

  public Task AnyMethod()
  {
    var res = _myHttpService.GetSpecificInformations();
    // do something
  }
```

```json
//appsettings.json
  // Specific settings, the setting key should match with the one used in the startup.cs
  "myEndpoint": {
    "Authentication": { 
      // Authentication object
      "Credentials": {
        "client_id": "xxxxx",
        "client_secret": "xxxxxx",
        "grant_type": "client_credentials",
        "resource": "my pointed ressource"
      },
      "URL": "https://myidentityprovider.com/xxxxx/oauth2/token",
      "AuthMethod": "OAUTH",
      "UseExpirationTime": false
    },
    "Url": "https://myOAuthProtectedUrl.com", // base url that should be consumed
    "HttpTimeoutInSeconds": 350, // indicates when the http client cut the socket waiting for a reply
    "PolicyTimeoutInSeconds": 100, // indicates when the retry policy cut the socket waiting for a reply
    "HandlerLifetime": 5, // sets the time in minute that a HttpMessageHandler instance can be reused from the pool
    "DefaultConnectionLimit": 50 // sets the maximum number of concurrent connections allowed
  }
```

### Logging
The logger that should reports into Elasticsearch.

```csharp
// Declaration in Startup.cs
public void ConfigureServices(IServiceCollection services)
{
  Log.Logger = SharedLibraries.Logging.LoggerFactory.GetELKLogger(Configuration);
}

// Service using the logger with DI
public class AnyService 
{
  private readonly ILogger _logger;

  public AnyService(ILogger<AnyService> logger)
  {
      _logger = logger;
  }

  public Task AnyMethod(dynamic myObject)
  {
    _logger.LogInformation("{method} has been invoked", nameof(AnyMethod)));
    // do something
  }

```

```json
//appsettings.json
  // Log general settings
  "Serilog": {
    "Using": [],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Information",
        "Elastic": "Warning",
        "Apm": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "Console"
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithProcessId",
      "WithThreadId"
    ],
    "Properties": {
      "ApplicationName": "ITF.Microservices.MessageHandlerSynchronizer" // name of service
    }
  },
 "ElasticSearchLog": {
    "ElasticSearchLog": "http://elasticsearch:9200/" // specific for the Serilog sink into Elasticsearch
  }
```

### APM
Provide automatic and manual transactions reporting as instrumental distributed analysis.

```csharp
// Declaration in Startup.cs
public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
  app.UseElasticSearchAPM(Configuration);
}

// Note that if a method is invoked through an action of a controller, the transaction is implicit. We create some when having business with background worker for example.
// We can also create sub transactions within a transaction called spans.
public class AnyService 
{
  public Task AnyMethod(dynamic myObject)
  {
    // Manual creation of transaction
      await Agent.Tracer
        .CaptureTransaction($"MyTransaction", "doingsomething", async (t) =>
        {
          _logger.LogInformation("{method} has been invoked", nameof(AnyMethod)));
          // do something
        });
  }
```

```json
//appsettings.json
  "ElasticApm": {
    "ServerUrls": "http://apm:8200", // APM server url
    "Enabled": true,
    "TransactionSampleRate": 1, // 100% of traffic is analysed
    "CaptureBody": "all", // get body from POST
    "CaptureHeaders": true, // get headers
    "SpanFramesMinDuration": 0, // no stacktrace except for exception
    "CloudProvider": "none" // not linked to a cloud provider
  }
```

### Elastic search client
When we need to persist or retrieve data from the NoSql engine.

#### Typed Repository
```csharp
// Declaration of object to be persisted with a string id
public class myPersistedObject : BaseElastic<myPersistedObject>
{
    public string specificField { get; set; }  

    // Example of specific mapping (if needed)
    public Func<PropertiesDescriptor<myPersistedObject>, IPromise<IProperties>> GetMappingProperties()
    {
        return p => p.Text(st => st
                    .Name(n => n.specificField)
                    .Fields(ff => ff
                        .SearchAsYouType(s => s
                            .Name(SEARCH_AS_YOU_TYPE))
                        .Keyword(k => k
                            .Name(KEYWORD)
                            .IgnoreAbove(256)
                            .Normalizer(NORMALIZER))))
    }
}

// Declaration of specific interface
public interface IMySpecificRepository : IElasticSearchRepository<myPersistedObject, string>
{
    Task<List<string>> GetSpecificInformations();
}


// Implementation of the previous interface
// Use of a typed repository
public class MySpecificRepository : ElasticSearchRepository<myPersistedObject, string>, IMySpecificRepository
{
    // Use the name of the object as collection name by convention
    public ElasticSearchRepository(IElasticClient elasticClient) : base(elasticClient, nameof(OrderNotification))
    {
        // Allow to apply specific mapping or AutoMap for myPersistedObject object
    }

    public Task<List<string>> GetSpecificInformations()
    {
        // ... do something
    }
}
```

#### UnTyped Repository (dedicated to partial update)
## When an unique repository linked to the same collection have multiple poco acting as partial update (subsets of a global poco)
```csharp

// Declaration of an empty flag interface (dedicated to filter on mapping)
public interface IMyObject
{
}

// Declaration of object to be persisted with a string id
public class myPersistedObject1 : BaseElastic<myPersistedObject1>, IMyObject
// Or public class myPersistedObject1 : BaseProjectedEvents<myPersistedObject1>, IMyObject // in case of projection with indempotency checks
{
    public string specificField { get; set; }  

    // Example of specific mapping (if needed)
    public Func<PropertiesDescriptor<myPersistedObject1>, IPromise<IProperties>> GetMappingProperties()
    {
        return p => p.Text(st => st
                    .Name(n => n.Name)
                    .Fields(ff => ff
                        .SearchAsYouType(s => s
                            .Name(SEARCH_AS_YOU_TYPE))
                        .Keyword(k => k
                            .Name(KEYWORD)
                            .IgnoreAbove(256)
                            .Normalizer(NORMALIZER))))
    }
}

public class myPersistedObject2 : BaseElastic<myPersistedObject2>, IMyObject
{
    public string specificField { get; set; }  

    // No specific mappinp, keep auto implemented interface
}

// Declaration of specific interface
public interface IMySpecificRepository : IElasticSearchUntypedRepository<IMyObject>
{
    Task<List<string>> GetSpecificInformations();
}


// Implementation of the previous interface
// Use of a typed repository
public class MySpecificRepository : ElasticSearchUntypedRepository<IMyObject>, IMySpecificRepository
{
    // Use the name of the object as collection name by convention
    public ElasticSearchRepository(IElasticClient elasticClient) : base(elasticClient, nameof(OrderNotification))
    {
      // Allow to apply specific mapping or AutoMap for ALL objects that implements IMappings<> open generic interface and IMyObject interface => myPersistedObject1 and myPersistedObject2 here
    }

    public Task<List<string>> GetSpecificInformations()
    {
        // ... do something
    }
}
```

```csharp
// Declaration in Startup.cs
public void ConfigureServices(IServiceCollection services)
{
  services.UseElasticSearch(Configuration);
  services.AddSingleton<IMySpecificRepository, MySpecificRepository>();
}
```


```csharp
// Service using the repository with DI
public class AnyService 
{
  private readonly IMySpecificRepository _myElkService;

  public AnyService(IMySpecificRepository myElkService)
  {
      _myElkService = myElkService;
  }

  public Task AnyMethod()
  {
    var res = _myElkService.GetSpecificInformations();
    // do something
  }
```

```json
//appsettings.json
  "ElasticSearch": {
    "Nodes": [
      {
        "NodeUrl": "http://elasticsearch:9200/" // the Elasticsearch node url
      }
    ]
  }
```

### Kafka client
When we need to publish or subscribe to a Kafka topic.

#### Publish

```csharp
// Declaration in Startup.cs
public void ConfigureServices(IServiceCollection services)
{
  services.UseKafkaPublisher(Configuration);
}
```

```csharp
// Any service using the publisher with DI
public class AnyService 
{
  private readonly IKafkaPublisher _kafkaPublisher;

  public AnyService(IKafkaPublisher kafkaPublisher)
  {
      _kafkaPublisher = kafkaPublisher;
  }

  public Task AnyMethod(dynamic myObject)
  {
    await _kafkaPublisher?.PublishAsync(myObject, "kafka_topic_example", myObject.Key);
    // do something
  }
```

```json
//appsettings.json
  "Kafka": {
    "BootStrapServers": "kafka:9092", // list of nodes
    "LingerMs": 0.5, // see official doc
    "QueueBufferingMaxKbytes": xx, // see official doc
    "BatchSize": xx, // see official doc
    "CompressionType": xx, // see official doc
    "Acks": xx, // see official doc
    "TopicsToCreateConfigurations": [
      // these topics are created on startup
      {
        "TopicName": "kafka_topic_example",
        "ReplicationFactor": 3, // number of broker (depends on the number of nodes)
        "NumberOfPartitions": 20, // number of partitions (to allow parallelism consumption),
        "RetentionMs": 86400000 // time in MS during while a message is kept in the topic
      }
    ]
  }
```

#### Consume

```csharp
// Declaration of a handler for a topic
public class myTopicHandler : KafkaBaseMessageHandler
{
  // We override a handler, we can here use pattern matching as the object is typed
  public override Task HandleMessage(object data) =>
  data switch
  {
      // Note that the types should be declared in the Shared Models library
      ObjectAMessage o => DoSomething(d),
      ObjectBMessage d => DoSomethingElse(d),
      _ => Task.CompletedTask
  };
}
  
```

```csharp
// Declaration in Startup.cs
public void ConfigureServices(IServiceCollection services)
{
  services.UseKafkaSubscribers<string, string>(Configuration,
      kafkaActionHandlers: new KafkaDelegateHandler[] {
              KafkaHandlerSupplier<IMessageHandler, myTopicHandler>
      });
}

// Note we can supply a middleware that can stop the listener on Kafka topics
// By default the healthchecks are available on 
// http://serviceaddress:port/admin/CancelBackgroundService
public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
  app.AddKakfaConsumerManagementRoutes();
}
```

```json
//appsettings.json
  "Kafka": {
    "BootStrapServers": "kafka:9092", // list of nodes
    "SubscriberConfigurations": [
      {
        "AutoOffsetReset": 1, // see official doc
        "ClassName": "myTopicHandler", // the name of the handler, should match
        "EnableAutoCommit": true, // see official doc
        "EnablePartitionEof": false, // see official doc
        "GroupId": "myTopicHandler.consumer", // the id of the group consuming the messages from the topic
        "SessionTimeouMs": 10000, // see official doc
        "StatisticsIntervalMs": 3000, // see official doc
        "TopicName": "topic_name", // the name of the topic subscribed
        // !!! Should be leaved null except if we explicitely want to process again already commited messages !!!
        // Could be usefull in case of crash or bad handling processing. 
        // Seek all offset per partitions for the current topic for a process event at the specified date and move the offsets accordingly
        "ProcessMessagesStartingDateOffset": "2021-08-03 8:32:17" 
      }
    ]
  }
```

### Azure bus subscription
When we need to subscribe to one or more Azure topic.

#### Publish

```csharp
// Declaration in Startup.cs
public void ConfigureServices(WebApplicationBuilder builder)
{
  // register services and hosted service
  builder.Services.AddAzureServiceBusMultipleSubscribersHostedService(builder.Configuration);
  // register the handler(s) that manage(s) specific messages
  builder.Services.AddKeyedTransient<ITF.SharedLibraries.AzureServiceBus.Subscriber.IMessageHandler, MyNotificationHandler>("messages");
  builder.Services.AddKeyedTransient<ITF.SharedLibraries.AzureServiceBus.Subscriber.IMessageHandler, MyNotificationHandler2>("products");
}
```

```csharp
// Any service using the publisher with DI
public class MyNotificationHandler(
    ILogger<MyNotificationHandler> logger) : ITF.SharedLibraries.AzureServiceBus.Subscriber.IMessageHandler
{
    public async Task HandleMessage(ProcessMessageEventArgs message, string? topic = null, string? subscription = null)
    {
        string data = message.Message.Body.ToString();
        logger.LogInformation("Received event from ASB {asbEvent}", data as string);

        // business logic here
    }
}
```

```json
//appsettings.json
  "AzureMultiSubscriptionsConfiguration": {
    "Subscriptions": [
      {
        "Name": "AzureBusProducts", // used to register the azure bus client then used in the hosted service
        "ConnectionString": "Endpoint=sb://xxxxx.servicebus.windows.net/;SharedAccessKeyName=only_listen;SharedAccessKey=abc...",
        "Handler": "products", // name used in the registration of the handler with AddKeyedTransient
        "TopicName": "main-topic",
        "SubscriptionName": "products",
        "MaxConcurrentCalls": 1,
        "AutoCompleteMessages": false
      },
      {
        "Name": "AzureBusMessages", // used to register the azure bus client then used in the hosted service
        "ConnectionString": "Endpoint=sb://xxxxx.servicebus.windows.net/;SharedAccessKeyName=only_listen;SharedAccessKey=abc...",
        "Handler": "messages",  // name used in the registration of the handler with AddKeyedTransient
        "TopicName": "main-topic",
        "SubscriptionName": "messages",
        "MaxConcurrentCalls": 1,
        "AutoCompleteMessages": false
      }
    ]
  }
```

### Healthchecks
Provide health checks for a MS and it's dependencies.

By default the healthchecks are available on http://serviceaddress:port/health

```csharp
// Declaration in Startup.cs
public void ConfigureServices(IServiceCollection services)
{
  services
      .AddHealthChecksMiddleware();

      // Note that depending of the ressource involved in the MS, several other healthchecks could be added :
      // .AddMongoDbHC(Configuration)
      // .AddEventStoreHC(Configuration)
      // .AddPostgresHC(Configuration)
      // .AddRabbitMqHC(Configuration)
      // .AddElasticSearchHC(Configuration)
      // .AddKafkaHC(Configuration)
}

public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
    app.UseEndpoints(endpoints =>
  {
      // HealthChecks
      endpoints.UseHealthChecks();
  });
}

```

```json
//appsettings.json
// The settings use the same JSON objects provided for any particular tool (Kafka, Elasticsearch etc ...)
```

### Metrics
Provide metrics for a MS.

By default the metrics are available on 
* http://serviceaddress:port/metrics
* http://serviceaddress:port/metrics-text
* http://serviceaddress:port/env

```csharp
// Declaration in Startup.cs
public void ConfigureServices(IServiceCollection services)
{
  services
      services.AddAllMetrics();
}

public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
    // Metrics middleware
    app.UseAllMetricsMiddleware()
        // Add a generic request middleware counting requests
        .UseMiddleware<RequestMiddleware>();
}

```

### Readyness
Provide a readyness route for a MS.

By default the readyness is available on 
* http://serviceaddress:port/readyness


```csharp
// Declaration in Startup.cs

public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
  app.UseEndpoints(endpoints =>
  {
      // Readyness
      endpoints.UseReadynessRoute();
  });
}

```

### Swagger
Provide a Swagger and a redoc for a MS.

By default the swagger is available on 
* http://serviceaddress:port/swagger

and the redoc on
* http://serviceaddress:port/redoc

```csharp
// Declaration in Startup.cs
public void ConfigureServices(IServiceCollection services)
{
  services.AddSwagger(Assembly.GetExecutingAssembly().GetName().Name);
}

public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
  app.UseSwaggerEndpoint(Assembly.GetExecutingAssembly().GetName().Name);
}
```

### Background services
Provide a background service template for a MS.

```csharp
// Declaration of a background worker
public class MyBackGroundWorker : CustomBackgroundService
{
  protected override async Task ExecuteAsync(CancellationToken stoppingToken)
  {
    // Prevent APM singleton implicit creation with default parameters
    await Task.Delay(5 * 1000, stoppingToken);
    while (!stoppingToken.IsCancellationRequested)
    {
      // do some recurring parallel task

      // Delay
        await Task.Delay(waitSomeMs, stoppingToken);
    }
  }
}

// Declaration in Startup.cs
public void ConfigureServices(IServiceCollection services)
{
  services.AddHostedService<MyBackGroundWorker>();
}
```

### Feature management
Provide feature management toggle for a MS.

```csharp
// Declaration in Startup.cs
public void ConfigureServices(IServiceCollection services)
{
  services.AddFeatureManagement();
}

// Note we can supply a middleware that can stop the listener on Kafka topics
// By default the healthchecks are available on 
// http://serviceaddress:port/admin/CancelBackgroundService
public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
{
  app.AddKakfaConsumerManagementRoutes();
}

// Any service using the feature flag manager with DI
public class AnyService 
{
  private readonly IFeatureManager _featureManager;

  public AnyService(IFeatureManager featureManager)
  {
      _featureManager = featureManager;
  }

  public Task AnyMethod(dynamic myObject)
  {
    if (await _featureManager.IsEnabledAsync("FeatureA"))
    {
        // do something
    }
  }
```

```json
//appsettings.json
"FeatureManagement": {
    "FeatureA": true, // Feature flag set to on
    "FeatureB": false, // Feature flag set to off
}
```
