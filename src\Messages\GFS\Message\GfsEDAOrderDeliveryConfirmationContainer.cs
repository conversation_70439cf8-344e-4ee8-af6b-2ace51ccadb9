﻿using ITF.SharedModels.Group.Enums.Gfs;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace ITF.SharedModels.Messages.GFS.Message
{

    public class GfsEDAOrderDeliveryConfirmationContainer
    {
        [JsonIgnore]
        public static readonly string NameType = "\"Delivery Confirmed for Order\"";

        [JsonProperty("Delivery Confirmed for Order")]
        public GfsOrderDeliveryConfirmation DeliveryConfirmedForOrder { get; set; } = new();
    }

    public class GfsOrderDeliveryConfirmation
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsDeliveryMethod DeliveryMethod { get; set; }
        public bool Rejected { get; set; }
        public string Text { get; set; } = default!;
        public DateTime DeliveredDate { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsDeliveredBy DeliveredBy { get; set; }
        public string ReceivedBy { get; set; } = default!;
        public string AttachedUrl { get; set; } = default!;
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageStatus MessageStatus { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageType MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public int? RelatedMessageId { get; set; }
        public bool Read { get; set; }
    }

}
