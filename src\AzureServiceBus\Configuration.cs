﻿namespace ITF.SharedLibraries.AzureServiceBus
{
    public class Configuration
    {
        public string ConnectionString { get; set; }
        public string TopicName { get; set; }
        public string SubscriptionName { get; set; }
        public int MaxConcurrentCalls { get; set; } = 1;
        public bool AutoCompleteMessages { get; set; } = false;
    }

    public class MultiSubscriptionsConfiguration
    {
        public List<Subscription> Subscriptions { get; set; } = new();
    }

    public class Subscription
    {
        /// <summary>
        /// Name used to register the azure service bus client
        /// </summary>
        public string Name { get; set; } = default!;
        public string ConnectionString { get; set; } = default!;
        /// <summary>
        /// Same key used to registered the handler with AddKeyedTransient on the startup
        /// </summary>
        public string Handler { get; set; } = default!;
        public string TopicName { get; set; } = default!;
        public string SubscriptionName { get; set; } = default!;
        public int MaxConcurrentCalls { get; set; } = 1;
        public bool AutoCompleteMessages { get; set; } = false;
    }

}
