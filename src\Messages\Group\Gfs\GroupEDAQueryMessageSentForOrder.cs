﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums.Gfs;
using ITF.SharedModels.Messages.GFS.Message;
using ITF.SharedModels.Messages.Italy;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using System.Reflection.Metadata;
using static ITF.SharedModels.Messages.Group.Gfs.Messages.V1;

namespace ITF.SharedModels.Messages.Group.Gfs
{
    public static partial class Messages
    {
        public static partial class V1
        {
            public class GroupEDAQueryMessageSentForOrder : BaseMessage<GroupEDAQueryMessageSentPayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey() => Guid.NewGuid().ToString();

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public GroupEDAQueryMessageSentForOrder()
                {
                    this.Payload = new GroupEDAQueryMessageSentPayload();
                }

                public static implicit operator GroupEDAQueryMessageSentForOrder(GfsEDAQueryMessageContainer v)
                {
                    GroupEDAQueryMessageSentForOrder msg = new();
                    msg.Payload = new();

                    msg.Payload.Subject = v.QuerySentForOrder.Subject;
                    msg.Payload.Text = v.QuerySentForOrder.Text;
                    msg.Payload.ApplicableFrom = v.QuerySentForOrder.ApplicableFrom;
                    msg.Payload.ApplicableTo = v.QuerySentForOrder.ApplicableTo;
                    msg.Payload.Id = v.QuerySentForOrder.Id;
                    msg.Payload.GFSgateNumber = v.QuerySentForOrder.GFSgateNumber;
                    msg.Payload.MessageStatus = v.QuerySentForOrder.MessageStatus;
                    msg.Payload.MessageType = v.QuerySentForOrder.MessageType;
                    msg.Payload.CreatedDate = v.QuerySentForOrder.CreatedDate;
                    msg.Payload.ModifiedDate = v.QuerySentForOrder.ModifiedDate;
                    msg.Payload.ConfirmedDate = v.QuerySentForOrder.ConfirmedDate;
                    msg.Payload.FromUnitID = v.QuerySentForOrder.FromUnitID;
                    msg.Payload.FromUnitMessageID = v.QuerySentForOrder.FromUnitMessageID;
                    msg.Payload.ToUnitID = v.QuerySentForOrder.ToUnitID;
                    msg.Payload.ToUnitMessageID = v.QuerySentForOrder.ToUnitMessageID;
                    msg.Payload.Operator = v.QuerySentForOrder.Operator;
                    msg.Payload.Priority = v.QuerySentForOrder.Priority;
                    msg.Payload.Read = v.QuerySentForOrder.Read;
                    msg.Payload.QueryMessageType = v.QuerySentForOrder.QueryMessageType;
                    msg.Payload.QueryFields = v.QuerySentForOrder.QueryFields;
                    msg.Payload.AttachedUrl = v.QuerySentForOrder.AttachedUrl;
                    msg.Payload.WaitingForResponse = v.QuerySentForOrder.WaitingForResponse;
                    msg.Payload.RelatedMessageId = v.QuerySentForOrder.RelatedMessageId;

                    return msg;
                }
            }
        }
    }

    public class GroupEDAQueryMessageSentPayload : LegacyPayload, IEquatable<GroupEDAQueryMessageSentPayload>
    {
        public DateTime? ApplicableFrom { get; set; }
        public DateTime? ApplicableTo { get; set; }
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageStatus MessageStatus { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageType MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public bool Read { get; set; }
        public string Subject { get; set; } = default!;
        public string Text { get; set; } = default!;
        public int QueryMessageType { get; set; } = default!;
        public List<GfsQueryField> QueryFields { get; set; } = default!;
        public string AttachedUrl { get; set; } = default!;
        public bool WaitingForResponse { get; set; } = default!;
        public int? RelatedMessageId { get; set; }

        public bool Equals(GroupEDAQueryMessageSentPayload parameter)
        {
            return (ApplicableFrom.Equals(parameter.ApplicableFrom) &&
                    ApplicableTo.Equals(parameter.ApplicableTo) &&
                    Id == parameter.Id &&
                    GFSgateNumber == parameter.GFSgateNumber &&
                    MessageStatus == parameter.MessageStatus &&
                    MessageType.Equals(parameter.MessageType) &&
                    CreatedDate.Equals(parameter.CreatedDate) &&
                    ModifiedDate.Equals(parameter.ModifiedDate) &&
                    ConfirmedDate.Equals(parameter.ConfirmedDate) &&
                    FromUnitID.Equals(parameter.FromUnitID) &&
                    FromUnitMessageID.Equals(parameter.FromUnitMessageID) &&
                    ToUnitID == parameter.ToUnitID &&
                    ToUnitMessageID == parameter.ToUnitMessageID &&
                    Operator == parameter.Operator &&
                    Priority == parameter.Priority &&
                    RelatedMessageId == parameter.RelatedMessageId &&
                    Read == parameter.Read &&

                    Subject == parameter.Subject &&
                    Text == parameter.Text &&
                    QueryMessageType == parameter.QueryMessageType &&
                    QueryFields.Equals(parameter.QueryFields) &&
                    AttachedUrl==parameter.AttachedUrl &&
                    WaitingForResponse==parameter.WaitingForResponse &&
                    RelatedMessageId==parameter.RelatedMessageId
                    );
        }

        public override bool Equals(object obj)
        {
            return Equals(obj as GroupEDAQueryMessageSentPayload);
        }

        public override int GetHashCode() => new
        {
            ApplicableFrom,
            ApplicableTo,
            Id,
            GFSgateNumber,
            MessageStatus,
            MessageType,
            CreatedDate,
            ModifiedDate,
            ConfirmedDate,
            FromUnitID,
            FromUnitMessageID,
            ToUnitID,
            ToUnitMessageID,
            Operator,
            Priority,
            Read,
            Subject,
            Text,
            QueryMessageType,
            QueryFields,
            AttachedUrl,
            WaitingForResponse,
            RelatedMessageId
        }.GetHashCode();
    }
}
