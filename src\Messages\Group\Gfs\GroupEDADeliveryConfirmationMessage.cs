﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums.Gfs;
using ITF.SharedModels.Messages.GFS.Message;
using ITF.SharedModels.Messages.Italy;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace ITF.SharedModels.Messages.Group.Gfs
{

    public static partial class Messages
    {
        public static partial class V1
        {
            public class GroupEDADeliveryConfirmationMessage : BaseMessage<GroupEDADeliveryConfirmationMessagePayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey() => Guid.NewGuid().ToString();

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public GroupEDADeliveryConfirmationMessage()
                {
                    this.Payload = new GroupEDADeliveryConfirmationMessagePayload();
                }

                public static implicit operator GroupEDADeliveryConfirmationMessage(GfsEDAOrderDeliveryConfirmationContainer v)
                {
                    GroupEDADeliveryConfirmationMessage msg = new();
                    msg.Payload = new();
                    msg.Payload.DeliveryMethod = v.DeliveryConfirmedForOrder.DeliveryMethod;
                    msg.Payload.Rejected = v.DeliveryConfirmedForOrder.Rejected;
                    msg.Payload.Text = v.DeliveryConfirmedForOrder.Text;
                    msg.Payload.DeliveredDate = v.DeliveryConfirmedForOrder.DeliveredDate;
                    msg.Payload.DeliveredBy = v.DeliveryConfirmedForOrder.DeliveredBy;
                    msg.Payload.ReceivedBy = v.DeliveryConfirmedForOrder.ReceivedBy;
                    msg.Payload.AttachedUrl = v.DeliveryConfirmedForOrder.AttachedUrl;
                    msg.Payload.Id = v.DeliveryConfirmedForOrder.Id;
                    msg.Payload.GFSgateNumber = v.DeliveryConfirmedForOrder.GFSgateNumber;
                    msg.Payload.MessageStatus = v.DeliveryConfirmedForOrder.MessageStatus;
                    msg.Payload.MessageType = v.DeliveryConfirmedForOrder.MessageType;
                    msg.Payload.CreatedDate = v.DeliveryConfirmedForOrder.CreatedDate;
                    msg.Payload.ModifiedDate = v.DeliveryConfirmedForOrder.ModifiedDate;
                    msg.Payload.ConfirmedDate = v.DeliveryConfirmedForOrder.ConfirmedDate;
                    msg.Payload.FromUnitID = v.DeliveryConfirmedForOrder.FromUnitID;
                    msg.Payload.FromUnitMessageID = v.DeliveryConfirmedForOrder.FromUnitMessageID;
                    msg.Payload.ToUnitID = v.DeliveryConfirmedForOrder.ToUnitID;
                    msg.Payload.ToUnitMessageID = v.DeliveryConfirmedForOrder.ToUnitMessageID;
                    msg.Payload.Operator = v.DeliveryConfirmedForOrder.Operator;
                    msg.Payload.Priority = v.DeliveryConfirmedForOrder.Priority;
                    msg.Payload.RelatedMessageId = v.DeliveryConfirmedForOrder.RelatedMessageId;
                    msg.Payload.Read = v.DeliveryConfirmedForOrder.Read;
                    return msg;
                }
            }
        }
    }


    public class GroupEDADeliveryConfirmationMessagePayload : LegacyPayload, IEquatable<GroupEDADeliveryConfirmationMessagePayload>
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsDeliveryMethod DeliveryMethod { get; set; }
        public bool Rejected { get; set; }
        public string Text { get; set; } = default!;
        public DateTime DeliveredDate { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsDeliveredBy DeliveredBy { get; set; }
        public string ReceivedBy { get; set; } = default!;
        public string AttachedUrl { get; set; } = default!;
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageStatus MessageStatus { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageType MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public int? RelatedMessageId { get; set; }
        public bool Read { get; set; }

        public bool Equals(GroupEDADeliveryConfirmationMessagePayload parameter)
        {
            return (DeliveryMethod == parameter.DeliveryMethod &&
                    Rejected == parameter.Rejected && 
                    Text == parameter.Text &&
                    DeliveredDate.Equals(parameter.DeliveredDate) &&
                    DeliveredBy == parameter.DeliveredBy &&
                    ReceivedBy == parameter.ReceivedBy &&
                    AttachedUrl == parameter.AttachedUrl &&
                    Id == parameter.Id &&
                    GFSgateNumber == parameter.GFSgateNumber &&
                    MessageStatus == parameter.MessageStatus &&
                    MessageType == parameter.MessageType &&
                    CreatedDate == parameter.CreatedDate &&
                    ModifiedDate == parameter.ModifiedDate &&
                    ConfirmedDate == parameter.ConfirmedDate &&
                    FromUnitID == parameter.FromUnitID &&
                    FromUnitMessageID == parameter.FromUnitMessageID &&
                    ToUnitID == parameter.ToUnitID &&
                    ToUnitMessageID == parameter.ToUnitMessageID &&
                    Operator == parameter.Operator &&
                    Priority == parameter.Priority &&
                    RelatedMessageId == parameter.RelatedMessageId &&
                    Read == parameter.Read
                    );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GroupEDADeliveryConfirmationMessagePayload);
        }

        public override int GetHashCode() => new
        {
            DeliveryMethod,
            Rejected,
            Text,
            DeliveredDate,
            DeliveredBy,
            ReceivedBy,
            AttachedUrl,
            Id,
            GFSgateNumber,
            MessageStatus,
            MessageType,
            CreatedDate,
            ModifiedDate,
            ConfirmedDate,
            FromUnitID,
            FromUnitMessageID,
            ToUnitID,
            ToUnitMessageID,
            Operator,
            Priority,
            RelatedMessageId,
            Read
        }.GetHashCode();
    }
}
