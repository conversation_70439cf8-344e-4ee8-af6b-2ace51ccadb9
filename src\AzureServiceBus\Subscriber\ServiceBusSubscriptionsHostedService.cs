﻿using Azure.Messaging.ServiceBus;
using ITF.SharedLibraries.CustomBackgroundService;
using Microsoft.Extensions.Azure;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace ITF.SharedLibraries.AzureServiceBus.Subscriber
{
    public sealed class ServiceBusSubscriptionsHostedService(
        IAzureClientFactory<ServiceBusClient> factory,
        IServiceScopeFactory scopeFactory,
        IOptions<MultiSubscriptionsConfiguration> options,
        ILogger<ServiceBusSubscriptionsHostedService> logger,
        IHostApplicationLifetime applicationLifetime) : CriticalBackgroundService(applicationLifetime, logger), IDisposable
    {
        private readonly List<ServiceBusProcessor> _processors = new();
        private readonly IOptions<MultiSubscriptionsConfiguration> _options = options;
        private readonly IAzureClientFactory<ServiceBusClient> _factory = factory;
        private readonly IServiceScopeFactory _scopeFactory = scopeFactory;

        protected override async Task InfiniteProcessAsync(CancellationToken cancellationToken)
        {
            _logger.LogDebug("Starting the Azure service bus Topic consumer BackgroundService");
            var settings = _options.Value;

            // Create all processors
            foreach (var subscription in settings.Subscriptions)
            {
                var client = _factory.CreateClient(subscription.Name);
                var proc = client.CreateProcessor(
                    //topicName: s.Topic,
                    topicName: subscription.TopicName,
                    subscriptionName: subscription.SubscriptionName,
                    new ServiceBusProcessorOptions
                    {
                        MaxConcurrentCalls = subscription.MaxConcurrentCalls,
                        AutoCompleteMessages = subscription.AutoCompleteMessages,
                        //PrefetchCount = s.Prefetch ?? 0
                    });

                proc.ProcessMessageAsync += async args =>
                {
                    // Create a scope per message so handlers can use scoped deps (DbContext, etc.)
                    await using var scope = _scopeFactory.CreateAsyncScope();

                    // Resolve handler by key from config (e.g., "orders" or "products")
                    var handler = scope.ServiceProvider
                        .GetRequiredKeyedService<IMessageHandler>(subscription.Handler);

                    if(handler == null)
                    {
                        throw new Exception($"Azure multiple subscription configuration is wrong: handler for {subscription.Handler} is null or empty, have you registered the handler?");
                    }

                    try
                    {
                        await handler.HandleMessage(args);

                        // complete only if we disabled AutoComplete
                        if (!subscription.AutoCompleteMessages)
                            await args.CompleteMessageAsync(args.Message, args.CancellationToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Processing failed for {subscription.TopicName}/{subscription.SubscriptionName}");
                        // decide your poison: abandon / dead-letter / defer
                        await args.AbandonMessageAsync(args.Message, cancellationToken: args.CancellationToken);
                    }
                };

                proc.ProcessErrorAsync += args =>
                {
                    _logger.LogError(args.Exception,
                        $"{nameof(ServiceBusSubscriptionsHostedService)} error on {args.EntityPath} (source: {args.ErrorSource})");
                    return Task.CompletedTask;
                };

                _processors.Add(proc);
            }

            // Start all processors
            foreach (var p in _processors)
                await p.StartProcessingAsync(cancellationToken);

            try
            {
                await Task.Delay(Timeout.Infinite, cancellationToken);
            }
            finally
            {
                foreach (var p in _processors)
                    await p.StopProcessingAsync();
                foreach (var p in _processors)
                    await p.DisposeAsync();
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogDebug("Stopping the Azure service bus Topic consumer BackgroundService");

            foreach (var p in _processors)
                await p.StopProcessingAsync(stoppingToken);

            await base.StopAsync(stoppingToken);
        }

        public override async void Dispose()
        {
            await Dispose(true);
            GC.SuppressFinalize(this);
            base.Dispose();
        }

        protected async Task Dispose(bool disposing)
        {
            if (disposing)
                foreach (var p in _processors)
                    await p.DisposeAsync();
        }
    }
}
