﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Messages.Italy;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.Messages.France.Order;
public static partial class Messages
{
    public static partial class V1
    {
        public class OrderReclamationMessage : BaseMessage<OrderReclamationMessagePayload>, IMessageKey, IDistributedTracing
        {
            public string GetMessageKey()
                => Payload?.Version == 0 ? Payload.OrderId : Payload?.OrderId + "-" + Payload?.Version;

            public void SetDistributedTracingData(string distributedTracingData)
            {
                DistributedTracingData = distributedTracingData;
            }
        }
    }
}

public sealed class OrderReclamationMessagePayload : LegacyPayload
{
    public int Version { get; set; }
    public string BusinessUnit { get; set; } = string.Empty;
    public DateTime? DisputeUpdateDate { get; set; }
    public string DisputeId { get; set; } = string.Empty;
    public DateTime? DeliveryDate { get; set; }
    public string OrderId { get; set; } = string.Empty;
    public string FloristId { get; set; } = string.Empty;
    public string ProductId { get; set; } = string.Empty;
    public string ProductSizeId { get; set; } = string.Empty;
    public string ProductStyleId { get; set; } = string.Empty;
    public string ReclamationCode { get; set; } = string.Empty;
    public string ReclamationReason { get; set; } = string.Empty;
}
