﻿using System.Text.Json.Serialization;
using JsonIgnoreAttribute = System.Text.Json.Serialization.JsonIgnoreAttribute;

namespace IT.Microservices.AuthenticationApi.Login;

public record AuthenticationResponse(
    string access_token,
    int? expires_in,
    int? refresh_expires_in,
    string refresh_token,
    string token_type,
    string id_token,
        [property: JsonPropertyName("not-before-policy")] int? notbeforepolicy,
    string session_state,
    string scope
);

public record IntrospectionResponse(
    bool active,
    string? username = null,
    string? email = null,
    string? sub = null,
    long? exp = null,
    long? iat = null,
    string? client_id = null,
    string? scope = null
);

public record AuthenticationFailedResponse(string? error = null,string? error_description = null , [property: JsonIgnore] bool? isException = false);

