﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <UserSecretsId>02f6ecbb-6e11-43c0-8694-105088e11f24</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
	<ImplicitUsings>enable</ImplicitUsings>
	<Nullable>enable</Nullable>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>IT.Microservices.AuthenticationApi.xml</DocumentationFile>
    <NoWarn>1701;1702;1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="IT.SharedLibraries.CT" Version="2.106.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.1" />
  </ItemGroup>

	<ItemGroup>
		<Using Include="ITF.SharedLibraries.EnvironmentVariable" />
		<Using Include="ITF.SharedLibraries.HostBuilder.Extensions" />
		<Using Include="CSharpFunctionalExtensions" />
		<Using Include="ITF.Lib.Common.Error" />
		<Using Include="Newtonsoft.Json" />
		<Using Include="Newtonsoft.Json.Linq" />
		<Using Include="ITF.SharedLibraries.ApplicationMetrics.Extensions" />
		<Using Include="ITF.SharedLibraries.HealthCheck.Extensions" />
		<Using Include="ITF.SharedLibraries.Readyness.Extensions" />
		<Using Include="ITF.SharedLibraries.Swagger" />
		<Using Include="ITF.SharedLibraries.Framework" />
		<Using Include="ITF.SharedLibraries.Json" />
		<Using Include="ITF.SharedLibraries.FeatureFlags" />
		<Using Include="Microsoft.AspNetCore.Mvc" />
		<Using Include="Swashbuckle.AspNetCore.Annotations" />
		<Using Include="IT.Microservices.AuthenticationApi.Common" />
		<Using Include="System.Reflection" />
		<Using Include="ITF.SharedLibraries.ExtensionMethods" />
		<Using Include="Microsoft.AspNetCore.Mvc.ApiExplorer" />
		<Using Include="Prometheus" />
		<Using Include="ITF.SharedLibraries.HttpClient.Polly.Extensions" Static="true" />
		<Using Include="commercetools.Sdk.Api" />
		<Using Include="commercetools.Sdk.Api.Serialization" />
		<Using Include="Serilog" />
		<Using Include="commercetools.Sdk.Api.Extensions" />
		<Using Include="Microsoft.Extensions.Options" />
		<Using Include="ITF.SharedLibraries.HttpClient" />
		<Using Include="ITF.SharedLibraries.HttpClient.HttpClient" Alias="HttpClient" />
		<Using Include="commercetools.Base.Client" />
		<Using Include="System.Globalization" />
		<Using Include="ITF.SharedLibraries.Kafka.Publisher" />
		<Using Include="ITF.SharedLibraries.Kafka.Extensions" />
		<Using Include="ITF.SharedLibraries.Kafka" />
	</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\IT.SharedLibraries.CT\src\IT.SharedLibraries.CT.csproj" />
  </ItemGroup>
  
</Project>
