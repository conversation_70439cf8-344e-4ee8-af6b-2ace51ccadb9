﻿using FS.Keycloak.RestApiClient.Model;
using IT.SharedLibraries.CT.Customers;
using ITF.SharedLibraries.Keycloak;

namespace IT.Microservices.AuthenticationApi.Login;

public class LoginUseCase(ILogger<LoginUseCase> logger , IConfiguration conf , IKeycloakLoginHttpService keycloakHttpService , ICustomerService custService , SerializerService serializerService , IKeycloakService keycloakService , ILegacyLoginService? legacyLoginService = null) : ILoginUseCase
{
    public async Task<Result<AuthenticationResponse, AuthenticationFailedResponse>> Process(LoginRequest req)
    {
        if (string.IsNullOrWhiteSpace(req.Email) || string.IsNullOrWhiteSpace(req.Password))
            return new AuthenticationFailedResponse("InvalidRequestData", "One or more parameters are invalid");

        req = req with { Email = req.Email.Trim().ToLower() };

        var authUser =  await keycloakHttpService.LoginUser(conf["Keycloak:TokenEndpoint"],
            [
                new("client_id", conf["Keycloak:ClientId"]),new("client_secret", conf["Keycloak:ClientSecret"]),
                new("username", req.Email),new("password", req.Password),
                new("grant_type", "password"),new("scope", "openid")
            ]);

        // return the error from Keycloak if there is an Exceptional Error
        if(authUser.IsFailure && authUser.Error.isException == true)
            return authUser.Error;

        // in this case the user is not found or the credentials are bad
        if (authUser.IsFailure)
        {
            var res = await keycloakService.IsUserByUsernameExists(req.Email);

            if (res)
                return authUser.Error;
            else // the user dont exist with this email we can try to find if the user exist in legacy if yes we can just send a message to sync the user after
            {
                if (legacyLoginService is not null) // if we have a dedicated legacy country service we can try the process
                {
                    var legacyRes = await legacyLoginService.GetUserFromLegacy(req);

                    if (legacyRes.IsSuccess)
                    {
                        // here we found/Auth the user into LegacyService and cast it into a ICustomerDraft Group Ct User
                        // we can create the user into CT
                        var ctRes = await custService.CreateCustomer(legacyRes.Value);

                        if (ctRes is null)
                        {
                            logger.LogError("Error while creating/duplicating the user from LegacyService into CT for email : {email}", req.Email);
                            return authUser.Error;
                        }
                        // we can create the user into Keycloak
                        var keycloakRes = await keycloakService.CreateUser(new UserRepresentation
                        {
                            Enabled = true,EmailVerified = true,
                            FirstName = legacyRes.Value.FirstName,LastName = legacyRes.Value.LastName,
                            Username = legacyRes.Value.Email,Email = legacyRes.Value.Email,
                            Credentials = [ new CredentialRepresentation { Type = "password", Temporary = false, Value = req.Password } ]
                        });

                        if (!keycloakRes.StatusCode.IsSuccessStatusCode())
                        {
                            logger.LogError("Error while creating the user into Keycloak for email : {email} with the error : {error}", req.Email, keycloakRes.ErrorText);
                            return authUser.Error;
                        }

                        // at this step we have the customer in Keycloak and in CT so we can login the User to get an access token and return the user data

                        authUser = await keycloakHttpService.LoginUser(conf["Keycloak:TokenEndpoint"],
                            [
                                new("client_id", conf["Keycloak:ClientId"]),new("client_secret", conf["Keycloak:ClientSecret"]),
                                new("username", req.Email),new("password", req.Password),
                                new("grant_type", "password"),new("scope", "openid")
                            ]);

                        // return the error from Keycloak
                        return authUser;
                    }
                    else // in this case error on Hybris or the User dont exist in the DB so just reply the initial error
                        return authUser.Error;
                }
                else
                    return authUser.Error;
            }
        }
        else
        {
            var userData = await custService.GetByEmail(req.Email);

            if (userData is null)
            {
                logger.LogError("The User {email} exist in Keycloak but there are no user in CT for this Store / CT api Error so we block the login flow for now",req.Email);
                return new AuthenticationFailedResponse { error="invalid_credentials",error_description= "Invalid user credentials" };
            }

            return authUser;
        }

    }

    public async Task<Result<IntrospectionResponse, AuthenticationFailedResponse>> Process(IntrospectionRequest req)
    {
        if (string.IsNullOrWhiteSpace(req.AccessToken))
            return new AuthenticationFailedResponse("InvalidRequestData", "One or more parameters are invalid");

        return  await keycloakHttpService.IntrospectTokenAsync(req.AccessToken);
    }

    public async Task<Result<AuthenticationResponse, AuthenticationFailedResponse>> Process(RefreshTokenRequest req)
    {
        if (string.IsNullOrWhiteSpace(req.RefreshToken))
            return new AuthenticationFailedResponse("InvalidRequestData", "One or more parameters are invalid");

        var refreshResult = await keycloakHttpService.RefreshToken(req.RefreshToken);

        if (refreshResult.IsSuccess)
            return refreshResult.Value;
        else
            return refreshResult.Error;
    }

}

public interface ILoginUseCase
{
    public Task<Result<AuthenticationResponse, AuthenticationFailedResponse>> Process(LoginRequest req);
    Task<Result<IntrospectionResponse, AuthenticationFailedResponse>> Process(IntrospectionRequest req);
    Task<Result<AuthenticationResponse, AuthenticationFailedResponse>> Process(RefreshTokenRequest req);
}