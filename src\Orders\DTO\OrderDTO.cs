﻿using ITF.SharedModels.Group.Enums;
using System.Text.Json.Serialization;

namespace IT.SharedLibraries.CT.Orders.DTO
{
    public class OrderDTOBase
    {
        public long CTVersion { get; set; }
        public string CTId { get; set; }
        public DateTime OrderDate { get; set; }
        public DateTime? LimitAcceptedDate { get; set; }
        public bool New { get; set; }

        public string OrderId { get; set; }
        public string LegacyOrderId { get; set; }
        public decimal Price { get; set; }
        public string Sequencing { get; set; }
        public string Status { get; set; }
        public bool InProgress { get; set; }
        public bool ExternalCourierRequested { get; set; }
        public string OccasionCode { get; set; }
        public bool IsMourning { get; set; } = false;
        public bool IsModified { get; set; } = false;
        public int ItemNumber { get; set; }
        public string? ExecutingFloristInvoiceUrl { get; set; }
        public bool IsFloristInvoicePrinted { get; set; }
    }

    public class OrderDTO : OrderDTOBase
    {
        public decimal DeliveryCost { get; set; }
        public double? DeliveryDistance { get; set; }
        public string? Pdf { get; set; }
        public string? ExecutingFloristId { get { return ExecutingFlorist?.Id; } }
        public string? TransmitterFloristId { get { return TransmitterFlorist?.Id; } }     

        public FloristDTO ExecutingFlorist { get; set; } = new();
        public FloristDTO TransmitterFlorist { get; set; } = new();

        public List<ProductDTO> Products { get; set; } = new();
        public List<ProductDTO> Accessories { get; set; } = new();
        public List<string> ModifiedFields { get; set; } = new List<string>();
        public DeliveryDTO Delivery { get; set; } = new();
        public ExternalDeliveryDTO? ExternalDelivery { get; set; }
        public MessageDTO Message { get; set; } = new();
        public BuyerDTO Buyer { get; set; } = new();
    }

    public class FloristDTO
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public List<ContactDTO> Contacts { get; set; } = new();
    }

    public class ContactDTO
    {
        [JsonConverter(typeof(JsonStringEnumConverter))]
        public ContactTypeEnum Type { get; set; }
        public string Entry { get; set; }
    }

    public class ProductDTO
    {
        public string Image { get; set; }
        public string ProductCode { get; set; }
        public long Quantity { get; set; }
        public string Size { get; set; }
        public IDictionary<string, string> Description { get; set; }
        public string Composition { get; set; }
        // public List<CompositionDTO> Composition { get; set; }
        public decimal Price { get; set; }
        public decimal MarketingFee { get; set; }
        public string RibbonText { get; set; }
        public VatDTO Vat { get; set; } = new();
    }

    public class VatDTO
    {
        public decimal Rate { get; set; }
        public decimal Amount { get; set; }
    }

    public class CompositionDTO
    {
        public long Quantity { get; set; }
        public string Value { get; set; }
    }

    public class DeliveryDTO
    {
        public string Name { get; set; }
        public string LastName { get; set; }
        public string FirstName { get; set; }
        public string Phone { get; set; }
        public string Address { get; set; }
        public string Town { get; set; }
        public string PostalCode { get; set; }
        public string Country { get; set; }
        public string Region { get; set; }
        public string AddInfo { get; set; }
        public string DeliveryInstructions { get; set; }
        public DateTime DeliveryDate { get; set; }
        public string DeliveryTime { get; set; }
        public string DeliveryMoment { get; set; }
        public string DeliveryLocation { get; set; }
        public decimal ExecutingFloristDeliveryAmount { get; set; }
        public string Event { get; set; }
        public string DeliveryMode { get; set; }
        public string AP { get; set; }
        public string Colivex { get; set; } = "A VOIR !";
        public string ContactFirstName { get; set; }
        public string ContactLastName { get; set; }
        public string CompanyName { get; set; }
        public string AdditionalStreetInfo { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
    }

    public class ExternalDeliveryDTO
    {
        public string ExternalDeliveryService { get; set; }
        public bool ExternalDeliveryServiceRequested { get; set; }
        public DateTime? ExternalDeliveryScheduledDate { get; set; }
        public string ExternalDeliveryStatus { get; set; }
        public decimal ExternalDeliveryCost { get; set; }
        public string ExternalDeliveryTrackingCode { get; set; }
    }

    public class MessageDTO
    {
        public string Message { get; set; }
        public string Signature { get; set; }
    }

    public class BuyerDTO
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
    }
}
