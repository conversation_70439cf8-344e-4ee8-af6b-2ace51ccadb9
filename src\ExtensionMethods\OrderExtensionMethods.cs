﻿using App.Metrics.Formatters.Prometheus;
using commercetools.Sdk.Api.Models.Carts;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.Orders;
using commercetools.Sdk.Api.Models.Products;
using commercetools.Sdk.Api.Models.Types;
using IT.SharedLibraries.CT.CustomAttributes;
using System;
using System.Linq;
using System.Text;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;

namespace IT.SharedLibraries.CT.ExtensionMethods
{
    public static class OrderExtensionMethods
    {
        public static string GetLegacyOrderNumber(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.LEGACY_ORDER_NUMBER))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.LEGACY_ORDER_NUMBER].ToString();
            }
            return null;
        }
        public static string GetFloristOrderStatus(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.FLORIST_ORDER_STATUS))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.FLORIST_ORDER_STATUS].ToString();
            }
            return null;
        }
        public static string GetSrc(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.SRC))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.SRC].ToString();
            }
            return null;
        }
        public static string? GetUserId(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.USER_ID))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.USER_ID].ToString();
            }
            return null;
        }
        public static string GetInternalOrderId(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.INTERNAL_ORDER_ID))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.INTERNAL_ORDER_ID].ToString();
            }
            return null;
        }
        public static string GetExecutingFloristId(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID].ToString();
            }
            return null;
        }
        public static string GetTransmitterFloristId(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.TRANSMITTER_FLORIST_ID))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.TRANSMITTER_FLORIST_ID].ToString();
            }
            return null;
        }
        public static string GetDeliveryService(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.DELIVERY_SERVICE))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.DELIVERY_SERVICE].ToString();
            }
            return null;
        }
        public static string GetDeliveryStatus(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.DELIVERY_STATUS))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.DELIVERY_STATUS].ToString();
            }
            return null;
        }
        public static string GetDeliveryTrackingCode(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.DELIVERY_TRACKING_CODE))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.DELIVERY_TRACKING_CODE].ToString();
            }
            return null;
        }
        public static string GetOccasionCode(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.OCCASION_CODE))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.OCCASION_CODE].ToString();
            }
            return null;
        }
        public static string GetDeliveryMode(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.DELIVERY_MODE))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.DELIVERY_MODE].ToString();
            }
            return null;
        }
        public static string GetMessage(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.MESSAGE))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.MESSAGE].ToString();
            }
            return null;
        }
        public static string GetSignature(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.SIGNATURE))
            {
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.SIGNATURE].ToString();
            }
            return null;
        }
        public static string GeContactTitle(this IOrder order)
        {
            if (order.Custom != null && order.ShippingAddress.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.ShippingAddress.CONTACT_TITLE))
            {
                return order.ShippingAddress.Custom.Fields[CtOrderCustomAttributesNames.ShippingAddress.CONTACT_TITLE].ToString();
            }
            return null;
        }
        public static decimal? GetDeliveryExecutingAmount(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_DELIVERY_AMOUNT))
            {
                CentPrecisionMoney money = (CentPrecisionMoney)order.Custom.Fields[CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_DELIVERY_AMOUNT];
                return money != null ? money.CentAmount / (decimal)Math.Pow(10, money.FractionDigits) : null;
            }
            return null;
        }
        public static decimal? GetDeliveryCost(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.DELIVERY_COST))
            {
                CentPrecisionMoney money = (CentPrecisionMoney)order.Custom.Fields[CtOrderCustomAttributesNames.Order.DELIVERY_COST];
                return money != null ? money.CentAmount / (decimal)Math.Pow(10, money.FractionDigits) : null;
            }
            return null;
        }

        public static int? GetDeliveryDistance(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.TryGetValue(CtOrderCustomAttributesNames.Order.DELIVERY_DISTANCE, out object? value))
                return (int?)value;
            return null;
        }
        public static DateTime? GetToBeAcceptedBefore(this IOrder order)
        {
            DateTime limitAcceptedBeforeDate = DateTime.MinValue;
            string strLimitAcceptedBeforeDate = null;

            if (order?.Custom?.Fields?.Keys?.Contains(CtOrderCustomAttributesNames.Order.TO_BE_ACCEPTED_BEFORE) ?? false)
                strLimitAcceptedBeforeDate = order.Custom.Fields[CtOrderCustomAttributesNames.Order.TO_BE_ACCEPTED_BEFORE].ToString();

            if (DateTime.TryParse(strLimitAcceptedBeforeDate, System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out limitAcceptedBeforeDate))
                return limitAcceptedBeforeDate;
            
            return null;
        }
        public static bool GetDeliveryInProgress(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.DELIVERY_IN_PROGRESS)
                && order.Custom.Fields[CtOrderCustomAttributesNames.Order.DELIVERY_IN_PROGRESS] is bool)
            {
                return (bool)order.Custom.Fields[CtOrderCustomAttributesNames.Order.DELIVERY_IN_PROGRESS];
            }
            return false;
        }
        public static bool GetReadByExecutor(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.READ_BY_EXECUTING_FLORIST)
                && order.Custom.Fields[CtOrderCustomAttributesNames.Order.READ_BY_EXECUTING_FLORIST] is bool)
            {
                return (bool)order.Custom.Fields[CtOrderCustomAttributesNames.Order.READ_BY_EXECUTING_FLORIST];
            }
            return false;
        }
        public static bool GetIsDeliveryServiceRequested(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.DELIVERY_SERVICE_REQUESTED)
                && order.Custom.Fields[CtOrderCustomAttributesNames.Order.DELIVERY_SERVICE_REQUESTED] is bool)
            {
                return (bool)order.Custom.Fields[CtOrderCustomAttributesNames.Order.DELIVERY_SERVICE_REQUESTED];
            }
            return false;
        }
        public static bool GetExternalCourierRequested(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.EXTERNAL_COURIER_REQUESTED)
                && order.Custom.Fields[CtOrderCustomAttributesNames.Order.EXTERNAL_COURIER_REQUESTED] is bool)
            {
                return (bool)order.Custom.Fields[CtOrderCustomAttributesNames.Order.EXTERNAL_COURIER_REQUESTED];
            }
            return false;
        }

        public static string? GetExecutingFloristInvoiceUrl(this IOrder order)
        {
             if (order?.Custom?.Fields?.Keys?.Contains(CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_INVOICE_URL) ?? false)
                return order.Custom.Fields[CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_INVOICE_URL].ToString();

            return null;
        }

        public static bool GetIsModified(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.IS_MODIFIED)
                && order.Custom.Fields[CtOrderCustomAttributesNames.Order.IS_MODIFIED] is bool)
            {
                return (bool)order.Custom.Fields[CtOrderCustomAttributesNames.Order.IS_MODIFIED];
            }
            return false;
        }

        public static string GetCustomString(string key, IFieldContainer fields, string defaultValue = null)
        {
            if (fields?.ContainsKey(key) ?? false)
                defaultValue = fields[key].ToString();

            return defaultValue;           
        }

        public static decimal GetDeliveryPrice(this IOrder order)
        {
            if (order == null || order.ShippingInfo == null || order.ShippingInfo.Price == null)
            {
                return 0;
            }
            return order.ShippingInfo.Price.AmountToDecimal();
        }

        public static decimal GetTotalItemsPrice(this IOrder order)
        {
            decimal price = 0;
            if (order.LineItems != null && order.LineItems.Count > 0)
            {
                foreach (ILineItem item in order.LineItems)
                {
                    price += item.Price.Value.AmountToDecimal();
                }
            }
            else
            {
                foreach (ICustomLineItem item in order.CustomLineItems)
                {
                    price += item.Money.AmountToDecimal();
                }
            }
            return price;
        }

        public static DateTime? GetDeliveryScheduledDate(this IOrder order)
        {
            DateTime deliveryScheduledDate = DateTime.MinValue;
            string strDeliveryScheduledDate = null;

            if (order?.Custom?.Fields?.Keys?.Contains(CtOrderCustomAttributesNames.Order.DELIVERY_SCHEDULED_DATE) ?? false)
                strDeliveryScheduledDate = order.Custom.Fields[CtOrderCustomAttributesNames.Order.DELIVERY_SCHEDULED_DATE].ToString();

            if (DateTime.TryParse(strDeliveryScheduledDate, System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out deliveryScheduledDate))
                return deliveryScheduledDate;

            return null;
        }

        public static string GetOrderOriginSystem(this IOrder order)
        {
            string orderOriginSystem = string.Empty;

            if (order?.Custom?.Fields?.Keys?.Contains(CtOrderCustomAttributesNames.Order.ORDER_SOURCE) ?? false)
                orderOriginSystem = order?.Custom?.Fields?[CtOrderCustomAttributesNames.Order.ORDER_SOURCE]?.ToString() ?? string.Empty;

            return orderOriginSystem;
        }

        public static string GetOrderSalesOrigin(this IOrder order)
        {
            string salesOrigin = string.Empty;

            if (order?.Custom?.Fields?.Keys?.Contains(CtOrderCustomAttributesNames.Order.DEVICE) ?? false)
                salesOrigin = order?.Custom?.Fields?[CtOrderCustomAttributesNames.Order.DEVICE]?.ToString() ?? string.Empty;

            return salesOrigin;
        }
        public static DateTime? GetCreationDate(this IOrder order)
        {
            string? strCreationDate = null;

            if (order?.Custom?.Fields?.TryGetValue(CtOrderCustomAttributesNames.Order.ORDER_CREATION_DATE, out object? value) ?? false)
                strCreationDate = value?.ToString();

            if (DateTime.TryParse(strCreationDate, System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out DateTime deliveryDate))
                return deliveryDate;

            return null;
        }
        public static long? GetCreationDateTimestamp(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.TryGetValue(CtOrderCustomAttributesNames.Order.ORDER_CREATION_DATE_TIMESTAMP, out object? value))
                return (long?)value;
            return null;
        }
        public static bool GetFloristInvoicePrinted(this IOrder order)
        {
            if (order.Custom != null && order.Custom.Fields.Keys.Contains(CtOrderCustomAttributesNames.Order.FLORIST_INVOICE_PRINTED)
                && order.Custom.Fields[CtOrderCustomAttributesNames.Order.FLORIST_INVOICE_PRINTED] is bool)
            {
                return (bool)order.Custom.Fields[CtOrderCustomAttributesNames.Order.FLORIST_INVOICE_PRINTED];
            }
            return false;
        }
        public static string GetAdditionalData(this IOrder order)
        {
            string orderOriginSystem = string.Empty;

            if (order?.Custom?.Fields?.Keys?.Contains(CtOrderCustomAttributesNames.Order.ADDITIONAL_DATA) ?? false)
                orderOriginSystem = order?.Custom?.Fields?[CtOrderCustomAttributesNames.Order.ADDITIONAL_DATA]?.ToString() ?? string.Empty;

            return orderOriginSystem;
        }
        #region shipping address
        public static DateTime? GetDeliveryDate(this IOrder order)
        {
            string? strDeliveryDate = null;

            if (order?.ShippingAddress?.Custom?.Fields?.TryGetValue(CtOrderCustomAttributesNames.ShippingAddress.DATE, out object? value) ?? false)
                strDeliveryDate = value?.ToString();

            if (DateTime.TryParseExact(strDeliveryDate, "yyyy-MM-dd", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out DateTime deliveryDate))
                return deliveryDate;

            return null;
        }
        
        public static string? GetDeliveryMoment(this IOrder order)
        {
            if (order?.ShippingAddress?.Custom?.Fields?.TryGetValue(CtOrderCustomAttributesNames.ShippingAddress.MOMENT, out object? value) ?? false)
                return value?.ToString();
            return null;
        }
        public static string? GetDeliveryTime(this IOrder order)
        {
            if (order?.ShippingAddress?.Custom?.Fields?.TryGetValue(CtOrderCustomAttributesNames.ShippingAddress.TIME, out object? value) ?? false)
                return value?.ToString();
            return null;
        }

        public static double? GetDeliveryLatitude(this IOrder order)
        {
            if (order?.ShippingAddress?.Custom?.Fields?.TryGetValue(CtOrderCustomAttributesNames.ShippingAddress.LATITUDE, out object? value) ?? false)
                return Convert.ToDouble(value);
            return null;
        }

        public static double? GetDeliveryLongitude(this IOrder order)
        {
            if (order?.ShippingAddress?.Custom?.Fields?.TryGetValue(CtOrderCustomAttributesNames.ShippingAddress.LONGITUDE, out object? value) ?? false)
                return Convert.ToDouble(value);
            return null;
        }

        #endregion shipping address
        public static string BuildStreetAddress(this IOrder order, IAddress address)
        {
            StringBuilder stringBuilder = new();
            if (address != null)
            {
                if (address.StreetNumber != null)
                    stringBuilder.Append(address.StreetNumber);
                if (address.StreetName != null)
                    stringBuilder.Append(' ').Append(address.StreetName);
            }
            return stringBuilder.ToString().Trim();
        }
       


    }
}
