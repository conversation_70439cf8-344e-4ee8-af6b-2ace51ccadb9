﻿using App.Metrics.Formatters.Prometheus;
using commercetools.Base.Client;
using commercetools.Base.Client.Error;
using commercetools.Sdk.Api.Client;
using commercetools.Sdk.Api.Extensions;
using commercetools.Sdk.Api.Models.CartDiscounts;
using commercetools.Sdk.Api.Models.Carts;
using commercetools.Sdk.Api.Models.Channels;
using commercetools.Sdk.Api.Models.Common;
using commercetools.Sdk.Api.Models.OrderEdits;
using commercetools.Sdk.Api.Models.Orders;
using commercetools.Sdk.Api.Models.Products;
using commercetools.Sdk.Api.Models.ShippingMethods;
using commercetools.Sdk.Api.Models.Stores;
using commercetools.Sdk.Api.Models.Types;
using commercetools.Sdk.Api.Serialization;
using CSharpFunctionalExtensions;
using IT.SharedLibraries.CT.Carts;
using IT.SharedLibraries.CT.Channels;
using IT.SharedLibraries.CT.CustomAttributes;
using IT.SharedLibraries.CT.Exceptions;
using IT.SharedLibraries.CT.ExtensionMethods;
using IT.SharedLibraries.CT.Orders.Comparers;
using IT.SharedLibraries.CT.Orders.Services;
using IT.SharedLibraries.CT.Products;
using IT.SharedLibraries.CT.ProductTypes;
using IT.SharedLibraries.CT.Repository;
using IT.SharedLibraries.CT.Settings;
using IT.SharedLibraries.CT.ShippingMethods;
using IT.SharedLibraries.CT.Stores;
using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedLibraries.Alerting;
using ITF.SharedLibraries.ExtensionMethods;
using ITF.SharedModels.DataModels.Order;
using ITF.SharedModels.Group.Enums;
using ITF.SharedModels.Messages.Italy.Order.Legacy;
using ITF.SharedModels.Notifications.Business.Legacy.Payloads;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using MongoDB.Driver;
using static IT.SharedLibraries.CT.CustomAttributes.CtOrderCustomAttributesNames;
using static ITF.SharedModels.Messages.Italy.Order.Legacy.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Messages.Messages.V1;
using static ITF.SharedModels.Notifications.Business.Legacy.Payloads.Common;


namespace IT.SharedLibraries.CT.Orders
{
    public class OrderService : IOrderService
    {
        private readonly IClient _commerceToolsClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<OrderService> _logger;
        private readonly IOptionsMonitor<CommerceToolCustomSettings> _commonSettings;
        private readonly SerializerService _serializerService;
        private readonly IProductTypeService _productTypeService;
        private readonly IChannelService _channelService;
        private readonly IStoreService _storeService;
        private readonly IProductService _productService;
        private readonly ICartService _cartService;
        private readonly IShippingMethodService _shippingMethodService;
        private readonly IUnitOrderService? _unitOrderService;
        private readonly ISlackAlertService? _slackAlertService;
        private readonly IOrderLineItemsPricesRepository? _orderLineItemsPricesRepository;

        private readonly string _projectKey;
        private string _ctMourningProductTypeId;
        private string _ctChannelId;
        private string _ctStorelId;

        private const int STEM_SIZE = 6;

        public OrderService(IClient commerceToolsClient,
            IConfiguration configuration,
            ILogger<OrderService> logger,
            IOptionsMonitor<CommerceToolCustomSettings> commonSettings,
            SerializerService serializerService,
            IProductTypeService productTypeService,
            IChannelService channelService,
            IStoreService storeService,
            IProductService productService,
            ICartService cartService,
            IShippingMethodService shippingMethodService,
            IUnitOrderService? unitOrderService = null,
            ISlackAlertService? slackAlertService = null,
            IOrderLineItemsPricesRepository? orderLineItemsPricesRepository = null)
        {
            _commerceToolsClient = commerceToolsClient;
            _configuration = configuration;
            _logger = logger;
            _serializerService = serializerService;
            _productTypeService = productTypeService;
            _channelService = channelService;
            _storeService = storeService;
            _productService = productService;
            _cartService = cartService;
            _shippingMethodService = shippingMethodService;
            _unitOrderService = unitOrderService;
            _commonSettings = commonSettings;
            _projectKey = _configuration?.GetSection("Client:ProjectKey")?.Value ?? string.Empty;
            _slackAlertService = slackAlertService;
            _orderLineItemsPricesRepository = orderLineItemsPricesRepository;
        }

        public async Task Initialize()
        {
            var ctProductType = await _productTypeService.GetByKey(_commonSettings.CurrentValue.CtMoruningProductTypeKey);
            if (ctProductType == null)
            {
                throw new CtCustomException("Cannot retrieve product type for mourning product type");
            }
            _ctMourningProductTypeId = ctProductType.Id;

            // get channels
            var channel = await _channelService.GetByKey(_commonSettings.CurrentValue.LocalCountryChannelKey);
            if (channel == null)
            {
                throw new CtCustomException("Cannot retrieve channel for local country");
            }
            _ctChannelId = channel.Id;

            // get store
            var store = await _storeService.GetByKey(_commonSettings.CurrentValue.LocalCountryStoreKey);
            if (store == null)
            {
                throw new CtCustomException("Cannot retrieve store for local country");
            }
            _ctStorelId = store.Id;
        }

        public async Task<IOrder> HandleOrderCreated(GlobalOrderModel globalOrderModel)
        {
            _logger.LogInformation("Received {GlobalOrderModel}", globalOrderModel.Serialize());

            if (String.IsNullOrWhiteSpace(globalOrderModel?.DeliveryCountryCode) && !String.IsNullOrWhiteSpace(globalOrderModel?.Shipping?.CountryCode))
            {
                globalOrderModel.DeliveryCountryCode = globalOrderModel.Shipping.CountryCode;
            }

            if (String.IsNullOrWhiteSpace(globalOrderModel?.SenderCountryCode) && !String.IsNullOrWhiteSpace(globalOrderModel?.Billing?.CountryCode))
            {
                globalOrderModel.SenderCountryCode = globalOrderModel.Billing.CountryCode;
            }
            if (!String.IsNullOrWhiteSpace(globalOrderModel?.DeliveryCountryCode) && String.IsNullOrWhiteSpace(globalOrderModel?.Shipping?.CountryCode))
            {
                globalOrderModel.Shipping.CountryCode = globalOrderModel.DeliveryCountryCode;
            }
            if (!String.IsNullOrWhiteSpace(globalOrderModel?.SenderCountryCode) && String.IsNullOrWhiteSpace(globalOrderModel?.Billing?.CountryCode))
            {
                globalOrderModel.Billing.CountryCode = globalOrderModel.SenderCountryCode;
            }

            // replace country code "UK" with "GB" only for the delivery address
            if (!String.IsNullOrWhiteSpace(globalOrderModel?.DeliveryCountryCode) && globalOrderModel?.DeliveryCountryCode == "UK")
            {
                globalOrderModel.DeliveryCountryCode = "GB";
            }
            if (!String.IsNullOrWhiteSpace(globalOrderModel?.Shipping?.CountryCode) && globalOrderModel?.Shipping?.CountryCode == "UK")
            {
                globalOrderModel.Shipping.CountryCode = "GB";
            }

            if (String.IsNullOrWhiteSpace(_ctMourningProductTypeId))
            {
                throw new CtCustomException("Have you called the Initialize method before calling Create: mourning product type is missed");
            }
            if (String.IsNullOrWhiteSpace(_ctChannelId))
            {
                throw new CtCustomException("Have you called the Initialize method before calling Create: channel is missed");
            }
            if (String.IsNullOrWhiteSpace(_ctStorelId))
            {
                throw new CtCustomException("Have you called the Initialize method before calling Create: store is missed");
            }


            DeliveryModeTypeEnum deliveryMode = DeliveryModeTypeEnum.STANDARD;

            // create cart draft
            commercetools.Sdk.Api.Models.Carts.ICartDraft cartDraft = new CartDraft();
            cartDraft.Currency = globalOrderModel.CurrencyCode;
            //cartDraft.AnonymousId = System.Guid.NewGuid().ToString();
            cartDraft.CustomerEmail = globalOrderModel.Billing.Email;
            cartDraft.Store = new StoreResourceIdentifier { Id = _ctStorelId };

            cartDraft.SetCardMessage(globalOrderModel.CardMessage != null ? globalOrderModel.CardMessage : "");
            cartDraft.SetSignature(globalOrderModel.Signature != null ? globalOrderModel.Signature : "");
            cartDraft.SetOccasionCode(globalOrderModel.OccasionCode != null ? globalOrderModel.OccasionCode : "");
            cartDraft.SetDevice(globalOrderModel.Device != null ? globalOrderModel.Device : "UNKNOWN");
            cartDraft.SetIP(globalOrderModel.IP != null ? globalOrderModel.IP : "");
            cartDraft.SetTransmitterFloristId(globalOrderModel.SenderFloristIdentifier != null ? globalOrderModel.SenderFloristIdentifier : "");
            if(globalOrderModel.Status.IsNullOrEmpty())
                cartDraft.SetFloristOrderStatus(StatusHelper.GetStringValue(StatusEnum.NEW_ORDER));
            else
                cartDraft.SetFloristOrderStatus(globalOrderModel.Status.ToUpper());
            cartDraft.SetLegacyOrderNumber(globalOrderModel.LegacyOrderNumber);
            cartDraft.SetSRC(String.IsNullOrEmpty(globalOrderModel.Src) ? "PFS" : globalOrderModel.Src);
            cartDraft.SetExecutingFloristId(globalOrderModel.ExecutingFloristIdentifier != null ? globalOrderModel.ExecutingFloristIdentifier : "");
            cartDraft.SetExecutingFloristType(globalOrderModel.ExecutingFloristType != null ? globalOrderModel.ExecutingFloristType : "");
            cartDraft.SetIsInvoiceRequested(globalOrderModel.IsInvoiceRequested);
            cartDraft.SetOrderCreationDate(globalOrderModel.CreatedAt);

            var billingAddress = new AddressDraft
            {
                FirstName = globalOrderModel.Billing.FirstName,
                LastName = globalOrderModel.Billing.LastName,
                Country = globalOrderModel.Billing.CountryCode,
                PostalCode = globalOrderModel.Billing.ZipCode,
                City = globalOrderModel.Billing.City,
                Email = globalOrderModel.Billing.Email,
                //Phone = globalOrderModel.Billing.Mobile,
                Mobile = globalOrderModel.Billing.Mobile,
                StreetName = globalOrderModel.Billing.Address,
                StreetNumber = globalOrderModel.Billing.StreetNumber,
                AdditionalStreetInfo = globalOrderModel.Billing.AdditionalStreetInfo,
                Company = globalOrderModel.Billing.CompanyName,
                Region = globalOrderModel.Billing.Province

            };
            billingAddress.SetFiscalCode(globalOrderModel.Billing.FiscalCode);
            billingAddress.SetCompanyNumber(globalOrderModel.Billing.CompanyNumber);
            billingAddress.SetVatNumber(globalOrderModel.Billing.VatNumber);
            billingAddress.SetPec(globalOrderModel.Billing.Pec);
            billingAddress.SetInvoiceFirstName(globalOrderModel.Billing.InvoiceFirstName);
            billingAddress.SetInvoiceLastName(globalOrderModel.Billing.InvoiceLastName);
            cartDraft.BillingAddress = billingAddress;

            var shippingAddress = new AddressDraft
            {
                FirstName = globalOrderModel.Shipping.FirstName,
                LastName = globalOrderModel.Shipping.LastName,
                PostalCode = globalOrderModel.Shipping.ZipCode,
                City = globalOrderModel.Shipping.City,
                StreetName = globalOrderModel.Shipping.StreetName,
                StreetNumber = globalOrderModel.Shipping.StreetNumber,
                Country = globalOrderModel.Shipping.CountryCode,
                Mobile = globalOrderModel.Shipping.Mobile,
                AdditionalAddressInfo = globalOrderModel.Shipping.AdditionalAddressInfo,
                AdditionalStreetInfo = globalOrderModel.Shipping.AdditionalStreetInfo,
                Company = globalOrderModel.Shipping.CompanyName,
                Region = globalOrderModel.Shipping.Province,
                //Region
            };
            shippingAddress.State = _unitOrderService.GetState(globalOrderModel.Shipping);

            shippingAddress.SetDate(globalOrderModel.Shipping.DeliveryDate);
            if (!string.IsNullOrWhiteSpace(globalOrderModel.Shipping.Time))
            {
                shippingAddress.SetTime(globalOrderModel.Shipping.Time);
            }
            shippingAddress.SetMoment(globalOrderModel.Shipping.Moment);
            shippingAddress.SetLatitude(globalOrderModel.Shipping.Latitude);
            shippingAddress.SetLongitude(globalOrderModel.Shipping.Longitude);
            if (!String.IsNullOrWhiteSpace(globalOrderModel.Shipping.Comments))
            {
                shippingAddress.SetComments(globalOrderModel.Shipping.Comments);
            }
            shippingAddress.SetContactTitle(globalOrderModel.ContactTitle.ToString());
            shippingAddress.SetContactFirstName(globalOrderModel.ContactFirstName != null ? globalOrderModel.ContactFirstName : "");
            shippingAddress.SetContactLastName(globalOrderModel.ContactLastName != null ? globalOrderModel.ContactLastName : "");

            cartDraft.ShippingAddress = shippingAddress;

            cartDraft.Country = globalOrderModel.Shipping.CountryCode;

            _logger.LogInformation("Order.Shipping.CountryCode={CountryCode}, _commonSettings.CurrentValue.LocalCountryCode={LocalCountryCode}", globalOrderModel?.Shipping?.CountryCode != null ? globalOrderModel?.Shipping?.CountryCode : "null"
                , _commonSettings.CurrentValue.LocalCountryCode != null ? _commonSettings.CurrentValue.LocalCountryCode : "null");

            IShippingMethod shippingMethodInternationalOrder = null;
            List<LineItemPrice> mongoLineItemsPrice = [];
            var orderProducts = globalOrderModel.Products.GetLineItemsProducts();

            if (globalOrderModel.Shipping.CountryCode == _commonSettings.CurrentValue.LocalCountryCode)
            {
                cartDraft.LineItems = new List<ILineItemDraft>();
                orderProducts.FindAll(p => p.IsBundlePart() && !p.IsDiscountLineItem()).ForEach(p => mongoLineItemsPrice.Add(new LineItemPrice(p.ProductKey, p.VariantKey, p.ExecutingFloristAmount.HasValue ? p.ExecutingFloristAmount.Value : 0)));
                foreach (GlobalOrderProduct p in orderProducts.GroupByBundleProducts())
                {
                    if (!p.IsDiscountLineItem())
                    {
                        var result = await GetLineItemForLocalOrder(p, globalOrderModel.CurrencyCode);
                        if (result != null)
                        {
                            if (result.Product != null)
                            {
                                if (result.Product.ProductType != null && result.Product.ProductType.Id == _ctMourningProductTypeId)
                                {
                                    deliveryMode = DeliveryModeTypeEnum.CEREMONY;
                                }
                            }

                            if (result.LineItem != null)
                            {
                                if (cartDraft.LineItems.Any(li => li.ProductId == result.LineItem.ProductId && li.VariantId == result.LineItem.VariantId))
                                {
                                    var existingLineItem = cartDraft.LineItems.FirstOrDefault(li => li.ProductId == result.LineItem.ProductId && li.VariantId == result.LineItem.VariantId);
                                    existingLineItem.Quantity++;
                                }
                                else
                                {
                                    cartDraft.LineItems.Add(result.LineItem);
                                }
                            }

                            if (!String.IsNullOrWhiteSpace(result?.Product?.TaxCategory?.Obj?.Key))
                            {
                                p.TaxCategoryKey = result.Product.TaxCategory.Obj.Key;
                            }
                        }
                    }
                }
            }
            else
            {
                shippingMethodInternationalOrder = await _shippingMethodService.GetByKey(_commonSettings.CurrentValue.OutboundOrderShippingMethodKey);
                if (shippingMethodInternationalOrder != null)
                {
                    if (shippingMethodInternationalOrder.TaxCategory.Obj.Rates.Any())
                    {
                        decimal taxRate = shippingMethodInternationalOrder.TaxCategory.Obj.Rates.FirstOrDefault().Amount;

                        cartDraft.TaxMode = ITaxMode.External;
                        cartDraft.CustomLineItems = new List<ICustomLineItemDraft>();
                        foreach (GlobalOrderProduct p in orderProducts)
                        {
                            CustomLineItemDraft item = await GetCustomLineItemForForeignOrder(p, globalOrderModel.CurrencyCode, globalOrderModel.Shipping.CountryCode, globalOrderModel.Src , globalOrderModel.OrderNumber);
                            cartDraft.CustomLineItems.Add(item);
                        }

                        cartDraft.ExternalTaxRateForShippingMethod = new ExternalTaxRateDraft { Name = "External Tax Rate PFS", Amount = taxRate, Country = globalOrderModel.Shipping.CountryCode };
                    }
                    else
                    {
                        _logger.LogWarning("Shipping method with key {OutboundOrderShippingMethodKey} is invalid: no valid tax rate found", _commonSettings.CurrentValue.OutboundOrderShippingMethodKey);
                    }

                }
                else
                {
                    _logger.LogWarning("Cannot find shipping methods with key: {OutboundOrderShippingMethodKey}", _commonSettings.CurrentValue.OutboundOrderShippingMethodKey);
                }

            }

            //// set discount code
            //if (cartDraft.DiscountCodes == null)
            //{
            //    cartDraft.DiscountCodes = new List<string> { "GROUP3PHCCS94" };
            //}
            cartDraft.SetDeliveryMode(Enum.GetName(typeof(DeliveryModeTypeEnum), deliveryMode));

            string cartJson = cartDraft.Serialize(Serializer.SerializerType.CommerceTools, _serializerService);
            //Debug.WriteLine(cartJson);
            _logger.LogInformation($"Cart draft: {cartJson}");


            IOrder order = null;
            // create cart in CT
            ICart cart = await _cartService.Create(cartDraft);
            if (cart != null)
            {
                //POC for discount maybe todo in the future
                //var discountDraft = new CartDiscountDraft
                //{
                //    Name = new LocalizedString { { "fr", "Remise de 5€" } },
                //    Key = "REM",
                //    Value = new CartDiscountValueAbsoluteDraft
                //    {
                //        Money = new List<IMoney>
                //        {
                //            new Money { CentAmount = 500, CurrencyCode = "EUR" }
                //        }
                //    },
                //    Target = new CartDiscountLineItemsTarget { Predicate = "true"},
                //    CartPredicate = "true",
                //    SortOrder = "0.8",//priority
                //    IsActive = true,
                //    RequiresDiscountCode = false
                //};

                //var discountCart = _cartService.CreateDiscountCart(discountDraft);


                if (globalOrderModel.Shipping.CountryCode == _commonSettings.CurrentValue.LocalCountryCode)
                {
                    // asking to Ct what is the shipping method that can be used for the created cart
                    var shippingMethodsAvailable = await _shippingMethodService.GetByCartId(cart.Id);

                    IShippingMethod shippingMethodToApply = _unitOrderService.GetNationalShippingMethod(globalOrderModel, shippingMethodsAvailable);

                    if (shippingMethodToApply != null)
                    {
                        string shippingMethodJson = shippingMethodToApply.Serialize(Serializer.SerializerType.CommerceTools, _serializerService);
                        _logger.LogInformation($"Shipping method for order {globalOrderModel.OrderNumber} is {shippingMethodToApply.Name}: {shippingMethodJson}");

                        ICart existingCart = await _cartService.GetById(cart.Id);

                        if (existingCart.ShippingInfo == null)
                        {
                            existingCart.ShippingInfo = new ShippingInfo { ShippingMethod = new ShippingMethodReference { Id = shippingMethodToApply.Id }, };
                        }
                        else
                        {
                            existingCart.ShippingInfo.ShippingMethod = new ShippingMethodReference { Id = shippingMethodToApply.Id };
                        }
                        // update the cart adding the shipping method
                        cart = await _cartService.Update(cart, existingCart);
                    }
                    else
                    {
                        _logger.LogWarning($"Can't find any shipping method for cart with id {cart.Id}");
                    }

                    //if (shippingMethodsAvailable.Results.Count > 0)
                    //{
                    //    // if CT returns at least one shipping method, add it to the cart
                    //    // we get the method set as default in CT
                    //    ShippingMethod shippingMethodToApply = (ShippingMethod)shippingMethodsAvailable.Results.FirstOrDefault(sm => sm.Key != null && sm.Key.Equals(_commonSettings.CurrentValue.PfsShippingMethodKey));

                    //    if (globalOrderModel.OrderType.Equals(OrderTypeEnum.FUNERAL))
                    //    {
                    //        shippingMethodToApply = (ShippingMethod)shippingMethodsAvailable.Results.FirstOrDefault(sm => sm.Key != null && sm.Key.Equals(_commonSettings.CurrentValue.MourningShippingMethodKey));
                    //    }

                    //    string shippingMethodJson = shippingMethodToApply.Serialize(Serializer.SerializerType.CommerceTools, _serializerService);
                    //    _logger.LogInformation($"Shipping method for order {globalOrderModel.OrderNumber} is {shippingMethodToApply.Name}: {shippingMethodJson}");

                    //    ICart existingCart = await _cartService.GetById(cart.Id);

                    //    if (existingCart.ShippingInfo == null)
                    //    {
                    //        existingCart.ShippingInfo = new ShippingInfo { ShippingMethod = new ShippingMethodReference { Id = shippingMethodToApply.Id } };
                    //    }
                    //    else
                    //    {
                    //        existingCart.ShippingInfo.ShippingMethod = new ShippingMethodReference { Id = shippingMethodToApply.Id };
                    //    }
                    //    // update the cart adding the shipping method
                    //    cart = await _cartService.Update(cart, existingCart);
                    //}
                }
                else
                {

                    if (shippingMethodInternationalOrder != null)
                    {
                        decimal taxRate = 0;
                        decimal shippingAmount = 0;
                        ICart existingCart = await _cartService.GetById(cart.Id);
                        if (shippingMethodInternationalOrder.TaxCategory.Obj.Rates.Any() && shippingMethodInternationalOrder.ZoneRates.Any() &&
                            shippingMethodInternationalOrder.ZoneRates.FirstOrDefault().ShippingRates.Any())
                        {
                            taxRate = shippingMethodInternationalOrder.TaxCategory.Obj.Rates.FirstOrDefault().Amount;
                            var shippingPrice = ((commercetools.Sdk.Api.Models.ShippingMethods.ShippingRate)shippingMethodInternationalOrder.ZoneRates.FirstOrDefault().ShippingRates.FirstOrDefault()).Price;
                            shippingAmount = shippingPrice.CentAmount / (decimal)Math.Pow(10, shippingPrice.FractionDigits);

                            existingCart.ShippingInfo = new ShippingInfo
                            {
                                ShippingMethodName = "International shipping PFS",
                                ShippingRate = new ShippingRate
                                {
                                    Price = new CentPrecisionMoney { CentAmount = (long)taxRate, CurrencyCode = globalOrderModel.CurrencyCode }
                                }
                            };

                            // update the cart adding the shipping method
                            cart = await _cartService.UpdateInatCartAddShippingInfo(cart, existingCart, taxRate, shippingAmount, globalOrderModel.Shipping.CountryCode, globalOrderModel.CurrencyCode);
                        }
                        else
                        {
                            _logger.LogWarning("Shipping method with key {OutboundOrderShippingMethodKey} is invalid: no valid shipping amount found", _commonSettings.CurrentValue.OutboundOrderShippingMethodKey);
                        }
                    }

                    //existingCart.ShippingInfo = new ShippingInfo { ShippingMethodName = "International shipping PFS", ShippingRate = new ShippingRate { Price = new TypedMoney { CentAmount = (long)(_commonSettings.CurrentValue.OutboundOrderShippingTaxRate * 100), CurrencyCode = globalOrderModel.CurrencyCode } } };

                    // update the cart adding the shipping method
                    //cart = await _cartService.UpdateInatCartAddShippingInfo(cart, existingCart, _commonSettings.CurrentValue.OutboundOrderShippingTaxRate, _commonSettings.CurrentValue.OutboundOrderShippingAmount, globalOrderModel.Shipping.CountryCode, globalOrderModel.CurrencyCode);
                }



                // read the updated cart
                //ICart existingCart2 = await GetCartById(cart.Id);
                //if (existingCart2 != null)
                //{
                //    // create payment draft (with amazon pay)
                //    //PaymentDraft paymenttoCreate = CreatePaymentDraft(existingCart2);

                //    //PaymentManager paymentManager = new PaymentManager(_client);
                //    //var payment = await paymentManager.Add(paymenttoCreate);
                //    //if (payment != null)
                //    //{
                //    //    // if the payment is properly created => set it to the cart
                //    //    existingCart2.PaymentInfo = new PaymentInfo { Payments = new List<IPaymentReference>() { new PaymentReference { Id = payment.Id } } };
                //    //}
                //    //// update the cart adding the payment
                //    //cart = await cartManager.Update(cart, existingCart2);
                //}

                // transform the cart into an order with the given orderNumber
                order = await CreateOrderFromCart(cart, PaymentState.Paid, globalOrderModel.OrderNumber);

                if (order != null)
                {
                    _logger.LogInformation($"Order created in CT with id {order.Id}");
                    if (mongoLineItemsPrice.Any())
                    {
                        var orderLineItemsPrices = new OrderLineItemsPrices(order.Id, mongoLineItemsPrice);
                        await (_orderLineItemsPricesRepository?.ReplaceOneAsync(orderLineItemsPrices) ?? Task.CompletedTask);
                        _logger.LogInformation($"OrderLineItemsPrices inserted : {orderLineItemsPrices.Serialize()}");

                    }
                }
                else
                {
                    _logger.LogError($"Order creation in CT failed for cart with id {cart.Id}");
                }
            }
            return order;
        }

        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderAssigned"></param>
        /// <param name="internalOrderId"></param>
        /// <returns></returns>
        public async Task<IOrder?> HandleOrderAssigned(GlobalOrderAssigned orderAssigned, string internalOrderId, StatusEnum defaultStatus = StatusEnum.ASSIGNED, string executingfloristInvoiceUrl = null)
        {
            _logger.LogInformation("Received {GlobalOrderAssigned}", orderAssigned.Serialize());
            IOrder? order = null;
            if (orderAssigned != null && !String.IsNullOrWhiteSpace(orderAssigned.OrderIdentifier) && !String.IsNullOrWhiteSpace(orderAssigned.FloristIdentifier))
            {
                // we get the actual order from CT
                order = await GetById(orderAssigned.OrderIdentifier);
                if (order != null)
                {
                   FillProductsLineItemIdWithCtOrder(order, orderAssigned.Products);
                    order = await UpdateFloristOrderStatus(order, StatusHelper.GetStringValue(defaultStatus), orderAssigned.FloristIdentifier, orderAssigned.DeliveryAmount, orderAssigned.ToBeAcceptedBefore, internalOrderId, orderAssigned.Products, executingfloristInvoiceUrl);
                    _logger.LogInformation("The order with id {OrderIdentifier} has been assigned to florist {FloristIdentifier}", orderAssigned.OrderIdentifier, orderAssigned.FloristIdentifier);

                }
                else
                    _logger.LogWarning("The order with id {OrderIdentifier} has NOT been assigned to florist {FloristIdentifier}", orderAssigned.OrderIdentifier, orderAssigned.FloristIdentifier);
            }

            return order;
        }

        private void FillProductsLineItemIdWithCtOrder(IOrder order, List<GlobalOrderAssignedProduct> products)
        {
            if (products != null && products.Count > 0)
            {
                foreach (var p in products)
                {
                    var lineItem = order.LineItems.FirstOrDefault(ctp =>
                        (ctp.ProductKey == p.ProductKey && ctp.Variant.Key == p.VariantKey) ||
                        (ctp.ProductKey == p.ProductKey && ctp.Variant.Sku == p.VariantKey) ||
                        (ctp.ProductKey == p.ProductKey && p.VariantKey.Contains("-PL-"))); //don't need to check the variantKey for France, many variant of same product are not possible within same order

                    if (lineItem != null)
                        p.LineItemId = lineItem.Id;

                }
            }
        }

        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderCancelled"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderCancelled(GlobalOrderCancelled orderCancelled)
        {
            _logger.LogInformation("Received {GlobalOrderCancelled}", orderCancelled.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(orderCancelled.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateFloristOrderStatus(orderPreUpdate, StatusHelper.GetStringValue(StatusEnum.CANCELLED));
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", orderCancelled.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderDeliveryTimeUpdated"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderDeliveryTimeUpdated(GlobalOrderDeliveryTimeUpdated orderDeliveryTimeUpdated)
        {
            _logger.LogInformation("Received {GlobalOrderDeliveryTimeUpdated}", orderDeliveryTimeUpdated.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(orderDeliveryTimeUpdated.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateOrderDeliveryTimes(orderPreUpdate, orderDeliveryTimeUpdated.Moment, orderDeliveryTimeUpdated.Time);
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", orderDeliveryTimeUpdated.OrderIdentifier);
            }

            return order;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderDeliveryStatusUpdated"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderDeliveryStatusUpdated(GlobalOrderDeliveryStatusUpdated orderDeliveryStatusUpdated)
        {
            _logger.LogInformation("Received {GlobalOrderDeliveryStatusUpdated}", orderDeliveryStatusUpdated.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(orderDeliveryStatusUpdated.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateOrderDeliveryStatus(orderPreUpdate, orderDeliveryStatusUpdated.DeliveryStatus);
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", orderDeliveryStatusUpdated.OrderIdentifier);
            }

            return order;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderDeliveryCostUpdated"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderDeliveryCostUpdated(GlobalOrderDeliveryCostUpdated orderDeliveryCostUpdated)
        {
            _logger.LogInformation("Received {GlobalOrderDeliveryCostUpdated}", orderDeliveryCostUpdated.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(orderDeliveryCostUpdated.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateOrderDeliveryCost(orderPreUpdate, orderDeliveryCostUpdated.DeliveryCost);
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", orderDeliveryCostUpdated.OrderIdentifier);
            }

            return order;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderDeliveryDateUpdated"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderDeliveryDateUpdated(GlobalOrderDeliveryDateUpdated orderDeliveryDateUpdated)
        {
            _logger.LogInformation("Received {LegacyOrderDeliveryDateUpdated}", orderDeliveryDateUpdated.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(orderDeliveryDateUpdated.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateOrderDeliveryDate(orderPreUpdate, orderDeliveryDateUpdated.Date);
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", orderDeliveryDateUpdated.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderCardMessageUpdated"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderCardMessageUpdated(GlobalOrderCardMessageUpdated orderCardMessageUpdated)
        {
            _logger.LogInformation("Received {LegacyOrderCardMessageUpdated}", orderCardMessageUpdated.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(orderCardMessageUpdated.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateOrderCardMessage(orderPreUpdate, orderCardMessageUpdated.CardMessage);
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", orderCardMessageUpdated.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderNotesUpdated"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderNotesUpdated(GlobalOrderNotesUpdated orderNotesUpdated)
        {
            _logger.LogInformation("Received {LegacyOrderCardMessageUpdated}", orderNotesUpdated.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(orderNotesUpdated.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateOrderNotes(orderPreUpdate, orderNotesUpdated.Notes);
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", orderNotesUpdated.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderDeliveryAddressUpdated"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderDeliveryAddressUpdated(GlobalOrderDeliveryAddressUpdated orderDeliveryAddressUpdated)
        {
            _logger.LogInformation("Received {LegacyOrderCardMessageUpdated}", orderDeliveryAddressUpdated.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(orderDeliveryAddressUpdated.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateOrderDeliveryAddress(orderPreUpdate, orderDeliveryAddressUpdated.Address, orderDeliveryAddressUpdated.City, orderDeliveryAddressUpdated.ZipCode,
                    orderDeliveryAddressUpdated.Latitude, orderDeliveryAddressUpdated.Longitude);
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", orderDeliveryAddressUpdated.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderDeliveredOnBehalf(GlobalOrderDeliveredOnBehalf payload)
        {
            _logger.LogInformation("Received {payload}", payload.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(payload.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateFloristOrderStatus(orderPreUpdate, StatusHelper.GetStringValue(StatusEnum.DELIVERED));
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", payload.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderAccepted(GlobalOrderAccepted payload)
        {
            _logger.LogInformation("Received {payload}", payload.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(payload.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateFloristOrderStatus(orderPreUpdate, StatusHelper.GetStringValue(StatusEnum.ACCEPTED));
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", payload.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderRejected(GlobalOrderRejected payload)
        {
            _logger.LogInformation("Received {payload}", payload.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(payload.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateFloristOrderStatus(orderPreUpdate, StatusHelper.GetStringValue(StatusEnum.REFUSED));
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", payload.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderDelivered(GlobalOrderDelivered payload)
        {
            _logger.LogInformation("Received {payload}", payload.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(payload.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateFloristOrderStatus(orderPreUpdate, StatusHelper.GetStringValue(StatusEnum.DELIVERED));
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", payload.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderItemUpdated"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderItemUpdated(GlobalOrderItemUpdated orderItemUpdated)
        {
            _logger.LogInformation("Received {LegacyOrderDeliveryDateUpdated}", orderItemUpdated.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(orderItemUpdated.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateOrderItem(orderPreUpdate, orderItemUpdated);
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", orderItemUpdated.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Try to update lineItems ofan order with provided actions
        /// </summary>
        /// <param name="orderItemUpdated"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderLineItemsUpdated((string ctOrderId, List<IOrderEditUpdateAction> actions)param)
        {
            IOrder orderPreUpdate = await GetById(param.ctOrderId);
            if (orderPreUpdate != null)
            {
               await UpdateOrderLineItems(orderPreUpdate, param.actions);
            }
            else
            {
                _logger.LogWarning("Order with id {ctOrderId} NOT found", param.ctOrderId);
            }
            return orderPreUpdate;

        }
        /// <summary>
        /// Try to update CT Fields ofan order with provided actions
        /// </summary>
        /// <param name="orderItemUpdated"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderCTFieldsUpdated((string ctOrderId, List<IOrderUpdateAction> actions) param)
        {
            IOrder orderPreUpdate = await GetById(param.ctOrderId);
            if (orderPreUpdate != null)
            {
                await CallOrderUpdate(orderPreUpdate, param.actions);
            }
            else
            {
                _logger.LogWarning("Order with id {ctOrderId} NOT found", param.ctOrderId);
            }
            return orderPreUpdate;

        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="orderItemUpdated"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderItemExecutorAmountUpdated(GlobalOrderItemExecutorAmountUpdated orderItemExecutorAmountUpdated)
        {
            _logger.LogInformation("Received {GlobalOrderItemExecutorAmountUpdated}", orderItemExecutorAmountUpdated.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            if (orderItemExecutorAmountUpdated != null && !String.IsNullOrWhiteSpace(orderItemExecutorAmountUpdated.OrderIdentifier))
            {
                orderPreUpdate = await GetById(orderItemExecutorAmountUpdated.OrderIdentifier);
                if (orderPreUpdate != null)
                {
                    string? lineItemId = null;
                    if (orderPreUpdate.LineItems.Any(ctp => ctp.ProductKey == orderItemExecutorAmountUpdated.ProductKey && ctp.Variant.Key == orderItemExecutorAmountUpdated.VariantKey))
                    {
                        var lineItem = orderPreUpdate.LineItems.FirstOrDefault(ctp => ctp.ProductKey == orderItemExecutorAmountUpdated.ProductKey && ctp.Variant.Key == orderItemExecutorAmountUpdated.VariantKey);
                        if (lineItem != null)
                        {
                            lineItemId = lineItem.Id;
                        }
                    }
                    else if (orderPreUpdate.LineItems.Any(ctp => ctp.ProductKey == orderItemExecutorAmountUpdated.ProductKey && ctp.Variant.Sku == orderItemExecutorAmountUpdated.VariantKey))
                    {
                        var lineItem = orderPreUpdate.LineItems.FirstOrDefault(ctp => ctp.ProductKey == orderItemExecutorAmountUpdated.ProductKey && ctp.Variant.Sku == orderItemExecutorAmountUpdated.VariantKey);
                        if (lineItem != null)
                        {
                            lineItemId = lineItem.Id;
                        }
                    }
                    if (lineItemId != null)
                        order = await UpdateItemExecutorAmountStatus(orderPreUpdate, lineItemId, orderItemExecutorAmountUpdated.ExecutorAmount);
                    else _logger.LogWarning($"Cannot find any line item within order {orderItemExecutorAmountUpdated.OrderIdentifier} with product key {orderItemExecutorAmountUpdated.ProductKey} and variant key {orderItemExecutorAmountUpdated.ProductKey}");
                }
            }
            if (order != null)
            {
                _logger.LogInformation("The order with id {OrderIdentifier} has been update", orderItemExecutorAmountUpdated.OrderIdentifier);
            }
            else
            {
                _logger.LogWarning("The order with id {OrderIdentifier} has NOT been update", orderItemExecutorAmountUpdated.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderAssignationRemoved(GlobalOrderAssignationRemoved payload)
        {
            _logger.LogInformation("Received {LegacyOrderAssignationRemoved}", payload.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(payload.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                //order = await UpdateOrderAssignationRemoved(order, payload);
                if (orderPreUpdate.GetExecutingFloristId() != null && orderPreUpdate.GetExecutingFloristId() == payload.FloristIdentier)
                {
                    order = await UpdateFloristOrderStatus(orderPreUpdate, StatusHelper.GetStringValue(StatusEnum.NEW_ORDER), "", 0, null, "");
                }
                else
                {
                    _logger.LogWarning("Order with id {OrderIdentifier} NOT assigned to {FloristIdentier}", payload.OrderIdentifier, payload.FloristIdentier);
                }
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", payload.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderAcceptedOnBehalf(GlobalOrderAcceptedOnBehalf payload)
        {
            _logger.LogInformation("Received {payload}", payload.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(payload.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateFloristOrderStatus(orderPreUpdate, StatusHelper.GetStringValue(StatusEnum.ACCEPTED));
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", payload.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderRejectedOnBehalf(GlobalOrderRejectedOnBehalf payload)
        {
            _logger.LogInformation("Received {payload}", payload.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(payload.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateFloristOrderStatus(orderPreUpdate, StatusHelper.GetStringValue(StatusEnum.REFUSED));
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", payload.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderSent(GlobalOrderSent payload)
        {
            _logger.LogInformation("Received {payload}", payload.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(payload.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateFloristOrderStatus(orderPreUpdate, StatusHelper.GetStringValue(StatusEnum.SENT));
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", payload.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderRecipientNameUpdated(GlobalOrderRecipientNameUpdated payload)
        {
            _logger.LogInformation("Received {payload}", payload.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(payload.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateOrderRecipientName(orderPreUpdate, payload.RecipientFirstName, payload.RecipientLastName);
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", payload.OrderIdentifier);
            }

            return orderPreUpdate;
        }
        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderRecipientPhoneNumberUpdated(GlobalOrderRecipientPhoneNumberUpdated payload)
        {
            _logger.LogInformation("Received {payload}", payload.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(payload.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateOrderRecipientPhoneNumber(orderPreUpdate, payload.RecipientPhoneNumber);
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", payload.OrderIdentifier);
            }

            return orderPreUpdate;
        }

        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="payload"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderRecipientCoordinatesUpdated(GlobalOrderRecipientCoordinatesUpdated payload)
        {
            _logger.LogInformation("Received {payload}", payload.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(payload.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateOrderRecipientCoordinates(orderPreUpdate, payload.Latitude, payload.Longitude);
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", payload.OrderIdentifier);
            }

            return orderPreUpdate;
        }

        /// <summary>
        /// Returns the order before the update if any update is done, null otherwise
        /// </summary>
        /// <param name="externalDeliveryDTO"></param>
        /// <returns></returns>
        public async Task<IOrder> HandleOrderExternalDeliveryFieldsUpdated(GlobalOrderExternalDeliveryFieldsUpdated payload)
        {
            _logger.LogInformation("Received {payload}", payload.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(payload.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateOrderExternalDeliveryFields(orderPreUpdate, payload.ExternalDeliveryScheduledDate, payload.ExternalDeliveryServiceRequested, payload.ExternalDeliveryService, payload.ExternalDeliveryStatus, payload.ExternalDeliveryCost, payload.ExternalDeliveryTrackingCode);
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", payload.OrderIdentifier);
            }

            return order;
        }

        public async Task<IOrder> HandleOrderExternalDeliveryStatusUpdated(LegacyOrderDeliveryStatusUpdatedPayload payload)
        {
            _logger.LogInformation("Received {payload}", payload.Serialize());
            IOrder order, orderPreUpdate;
            order = orderPreUpdate = null;
            orderPreUpdate = await GetById(payload.OrderIdentifier);
            if (orderPreUpdate != null)
            {
                order = await UpdateOrderExternalDeliveryStatus(orderPreUpdate, payload.DeliveryStatus.ToString());
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", payload.OrderIdentifier);
            }

            return order;
        }

        public async Task<List<IOrder>> GetOrdersByExecutorAndDeliveryDate(string floristIdentifier, string deliveryDate_yyyy_MM_dd)
        {
            if (String.IsNullOrWhiteSpace(floristIdentifier))
                throw new ArgumentException("Argument Exception: floristIdentifier cannot be null or empty");

            List<IOrder> orders = new List<IOrder>();
            try
            {
                var results = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Orders()
                    .Get()
                    .WithWhere($"custom(fields(executingFloristId=\"{floristIdentifier}\")) and shippingAddress(custom(fields(date=\"{deliveryDate_yyyy_MM_dd}\")))")
                    .ExecuteAsync();
                if (results != null)
                {
                    orders = results.Results as List<IOrder>;
                }
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving order with floristIdentifier {floristIdentifier} and delviery date = {deliveryDate_yyyy_MM_dd} because of {nfex.Message} - {nfex.StackTrace}");
            }
            catch (Exception nfex)
            {
                _logger.LogError(nfex, $"Error while retrieving order with floristIdentifier {floristIdentifier} and delviery date = {deliveryDate_yyyy_MM_dd} because of {nfex.Message} - {nfex.StackTrace}");
            }
            return orders;
        }
        public async Task<IOrder> GetById(string id)
        {
            IOrder order = null;
            try
            {
                order = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Orders()
                    .WithId(id)
                    .Get()
                    .ExecuteAsync();
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving order with id {id}");
            }
            catch (commercetools.Base.Client.Error.UnauthorizedException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving order with id {id} - {nfex.Body}");
            }
            catch (Exception nfex)
            {
                _logger.LogError(nfex, $"Error while retrieving order with id {id} because of {nfex.Message} - {nfex.StackTrace}");
                Exception inner = nfex.InnerException;
                int innerCount = 0;
                while (inner != null)
                {
                    innerCount++;
                    _logger.LogError(nfex, $"Inner[{innerCount}] error while retrieving order with id {id} because of {nfex.Message} - {nfex.StackTrace}");
                    inner = inner.InnerException;
                }
            }
            return order;
        }

        public async Task<IOrder> GetByOrderNumber(string orderNumber)
        {
            IOrder order;
            try
            {
                order = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Orders()
                    .WithOrderNumber(orderNumber)
                    .Get()
                    .ExecuteAsync();
            }
            catch (NotFoundException nfex)
            {
                _logger.LogWarning(nfex, "Order with orderNumber {orderNumber} not found", orderNumber);
                order = null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get Order with orderNumber {orderNumber}", orderNumber);
                order = null;
            }
            return order;
        }

        #region RAO France Legacy Actions


        /// <summary>
        /// Method that handle the whole modification needed for CT from an OrderUpdatedMessage / OrderAssignmentMessage / OrderPlacededMessage Event from the RAO
        /// </summary>
        /// <param name="payload"> the original payload message from the RAO indicating that on order is updated into the RAO</param>
        /// <returns>Returns a bool indicating if we have sucessfully modified the order into CT or not</returns>
        public async Task<Result> HandleRAOLegacyOrderUpdate<T>(T? payload, IOrder ctOrder, List<KeyValuePair<OrderDifference, object>> fieldsUpdated) where T : BaseOrderPayload
            =>  await UpdateOrderFromRAOToCT(ctOrder, fieldsUpdated);

        /// <summary>
        /// Method that handle the whole modification needed for CT from an OrderManagementStatusMessage Event from the RAO
        /// </summary>
        /// <param name="message"> the original message from the RAO indicating that on order status have changed into the RAO</param>
        /// <returns>Returns a bool indicating if we have sucessfully modified the order into CT or not</returns>
        public async Task<bool> HandleRAOLegacyOrderManagementStatus(OrderManagementStatusMessage message)
        {
            var order = await GetByOrderNumber(message?.Payload?.OrderId); // the orderId in the message is the OrderId ITXXXXX into the RAO that match with the CT orderNumber not the CT ID
            if (order != null)
            {
                var result = await Result.Try(async () =>
                {
                    var status = GetCtStatusFromFrRAOStatus(message.Payload.ManagementStatus);
                    var orderupdate = CreateOrderUpdate(order, [new KeyValuePair<OrderDifference, object>(OrderDifference.Status, status)]);
                    await PostOrderUpdateWithRetry(orderupdate, order.Id, order.OrderNumber);
                });
                return result.IsSuccess;
            }
            else
            {
                _logger.LogWarning("Order with id {OrderIdentifier} NOT found", message?.Payload?.OrderId);
                return false;
            }
        }

        #endregion

        private async Task<IOrder> CreateOrderFromCart(ICart cart, PaymentState paymentState, string orderNumber)
        {
            IOrderFromCartDraft orderFromCartDraft = new OrderFromCartDraft
            {
                Cart = new CartResourceIdentifier { Id = cart.Id },
                Version = cart.Version,
                PaymentState = IPaymentState.Pending,
                OrderNumber = orderNumber
            };

            switch (paymentState)
            {
                case PaymentState.Paid:
                    orderFromCartDraft.PaymentState = IPaymentState.Paid;
                    break;
                case PaymentState.Pending:
                    orderFromCartDraft.PaymentState = IPaymentState.Pending;
                    break;

                default:
                    orderFromCartDraft.PaymentState = IPaymentState.Pending;
                    break;
            }

            IOrder order = null;
            try
            {
                order = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Orders()
                    .Post(orderFromCartDraft)
                    .ExecuteAsync();
            }
            catch (BadRequestException ex)
            {
                _logger.LogError(ex, $"Error while creating an order from cart with id {cart.Id}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error while creating an order from cart with id {cart.Id} because of {ex.Message} - {ex.StackTrace}");
                throw;
            }
            return order;
        }
        private async Task<IOrder> UpdateFloristOrderStatus(IOrder order, string new_status, string new_executor = null, decimal? new_delivery_amount = null, DateTime? toBeAcceptedBefore = null, string internalOrderId = null, List<GlobalOrderAssignedProduct> products = null, string executingFloristInvoiceURL = null)
        {
            _logger.LogInformation("UpdateFloristOrderStatus Order {OrderIdentifier} new status = {NewStatus}", order.Id, new_status);

            var actions = new List<IOrderUpdateAction>();
            actions.Add(new OrderSetCustomFieldAction
            {
                Name = CtOrderCustomAttributesNames.Order.FLORIST_ORDER_STATUS,
                Value = new_status
            });

            if (new_status.Equals(StatusHelper.GetStringValue(StatusEnum.ASSIGNED)) || 
                order.ShippingAddress.Country.Equals("FR", StringComparison.CurrentCultureIgnoreCase) && new_status.Equals(StatusHelper.GetStringValue(StatusEnum.ACCEPTED))) //to ensure that fr case without assignment state handle those following updated customField
            {
                actions.Add(new OrderSetCustomFieldAction
                {
                    Name = CtOrderCustomAttributesNames.Order.DELIVERY_IN_PROGRESS,
                    Value = false
                });
                actions.Add(new OrderSetCustomFieldAction
                {
                    Name = CtOrderCustomAttributesNames.Order.READ_BY_EXECUTING_FLORIST,
                    Value = false
                });
            }

            if (!string.IsNullOrWhiteSpace(new_executor) || new_status.Equals(StatusHelper.GetStringValue(StatusEnum.NEW_ORDER)))
            {
                actions.Add(new OrderSetCustomFieldAction
                {
                    Name = CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID,
                    Value = new_executor
                });
            }

            if (!string.IsNullOrWhiteSpace(executingFloristInvoiceURL))
            {
                actions.Add(new OrderSetCustomFieldAction
                {
                    Name = CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_INVOICE_URL,
                    Value = executingFloristInvoiceURL
                });
            }

            if (new_delivery_amount.HasValue)
            {
                actions.Add(new OrderSetCustomFieldAction
                {
                    Name = CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_DELIVERY_AMOUNT,
                    Value = new Money
                    {
                        CentAmount = (long)(new_delivery_amount.Value * 100),
                        CurrencyCode = order.TotalPrice.CurrencyCode
                    }
                });
            }

            if (!string.IsNullOrWhiteSpace(internalOrderId))
            {
                actions.Add(new OrderSetCustomFieldAction
                {
                    Name = CtOrderCustomAttributesNames.Order.INTERNAL_ORDER_ID,
                    Value = internalOrderId
                });
            }

            if (toBeAcceptedBefore.HasValue)
            {
                actions.Add(new OrderSetCustomFieldAction
                {
                    Name = CtOrderCustomAttributesNames.Order.TO_BE_ACCEPTED_BEFORE,
                    Value = toBeAcceptedBefore
                });
            }

            if (products != null && products.Count > 0)
            {
                foreach (var product in products)
                {
                    if (!string.IsNullOrEmpty(product.LineItemId))
                    {
                        actions.Add(new OrderSetLineItemCustomFieldAction
                        {
                            LineItemId = product.LineItemId,
                            Name = CtOrderCustomAttributesNames.LineItem.EXECUTING_FLORIST_AMOUNT,
                            Value = new CentPrecisionMoney { CentAmount = (long)(product.ExecutorAmount * 100), CurrencyCode = "EUR", FractionDigits = 2 }
                        });
                    }
                }
            }

            return await CallOrderUpdate(order, actions);
        }
    

        private async Task<IOrder> UpdateItemExecutorAmountStatus(IOrder order, string lineItemId, decimal executorAmount)
        {
            //OrderUpdate update = null;
            var actions = new List<IOrderUpdateAction>();

            actions.Add(new OrderSetLineItemCustomFieldAction
            {
                LineItemId = lineItemId,
                Name = CtOrderCustomAttributesNames.LineItem.EXECUTING_FLORIST_AMOUNT,
                Value = new CentPrecisionMoney { CentAmount = (long)(executorAmount * 100), CurrencyCode = "EUR", FractionDigits = 2 }
            });
            return await CallOrderUpdate(order, actions);
        }

        private async Task<IOrder> UpdateOrderDeliveryTimes(IOrder order, MomentEnum? moment, string? mourningTime)
        {
            var actions = new List<IOrderUpdateAction>();
            if(moment is not null)
                actions.Add(new OrderSetShippingAddressCustomFieldAction
                {
                    Name = ShippingAddress.MOMENT,
                    Value = ((int)moment).ToString()
                });
            if (!string.IsNullOrWhiteSpace(mourningTime))
                actions.Add(new OrderSetShippingAddressCustomFieldAction
                {
                    Name = ShippingAddress.TIME,
                    Value = mourningTime
                });
            return await CallOrderUpdate(order, actions);
        }

        private async Task<IOrder> UpdateOrderDeliveryStatus(IOrder order, DeliveryStatusEnum deliveryStatus)
        {
            var actions = new List<IOrderUpdateAction>();

            actions.Add(new OrderSetCustomFieldAction
            {
                Name = CtOrderCustomAttributesNames.Order.DELIVERY_STATUS,
                Value = deliveryStatus.ToString()
            });
            if (deliveryStatus.Equals(DeliveryStatusEnum.DELIVERED))
            {
                actions.Add
                (
                    new OrderSetCustomFieldAction()
                    {
                        Name = CtOrderCustomAttributesNames.Order.FLORIST_ORDER_STATUS,
                        Value = deliveryStatus.ToString()
                    }
                );
            }
            return await CallOrderUpdate(order, actions);
        }

        private async Task<IOrder> UpdateOrderDeliveryCost(IOrder order, decimal deliveryCost)
        {
            var actions = new List<IOrderUpdateAction>();

            actions.Add(new OrderSetCustomFieldAction
            {
                Name = CtOrderCustomAttributesNames.Order.DELIVERY_COST,
                Value = new CentPrecisionMoney { CentAmount = (long)(deliveryCost * 100), CurrencyCode = "EUR", FractionDigits = 2 }
            });
            return await CallOrderUpdate(order, actions);
        }
        private async Task<IOrder> UpdateOrderDeliveryAddress(IOrder order, string address, string city, string zipCode, double latitude, double longitude)
        {
            var actions = new List<IOrderUpdateAction>();
            if (order.ShippingAddress.City != city || order.ShippingAddress.PostalCode != zipCode || order.ShippingAddress.StreetName != address)
            {
                actions.Add(new OrderSetShippingAddressAction
                {
                    //Address = new BaseAddress
                    Address = new Address
                    {
                        City = city,
                        PostalCode = zipCode,
                        StreetName = address,
                        Country = order?.ShippingAddress?.Country, // if the delivery country is changed the order can't be delivered as it is, but it must be cancelled and a new one created
                        FirstName = order.ShippingAddress?.FirstName,
                        LastName = order.ShippingAddress?.LastName,
                        AdditionalAddressInfo = order.ShippingAddress?.AdditionalAddressInfo,
                        AdditionalStreetInfo = order.ShippingAddress?.AdditionalStreetInfo,
                        Apartment = order?.ShippingAddress?.Apartment,
                        Building = order.ShippingAddress?.Building,
                        Company = order?.ShippingAddress?.Company,
                        Email = order?.ShippingAddress?.Email,
                        Mobile = order?.ShippingAddress?.Mobile,
                        Phone = order?.ShippingAddress?.Phone,
                        Department = order?.ShippingAddress?.Department,
                        POBox = order?.ShippingAddress?.POBox,
                        Region = order?.ShippingAddress?.Region,
                        Salutation = order?.ShippingAddress?.Salutation,
                        State = order?.ShippingAddress?.State,
                        StreetNumber = order?.ShippingAddress?.StreetNumber,
                        Title = order?.ShippingAddress?.Title,
                        Custom = order?.ShippingAddress?.Custom,
                    }
                });
            }

            double currentLat = 0;
            double currentLong = 0;
            if (!order.ShippingAddress.Custom.Fields.Any(cf => cf.Key == CtOrderCustomAttributesNames.ShippingAddress.LATITUDE)
                ||
                    (order.ShippingAddress.Custom.Fields.FirstOrDefault(cf => cf.Key == CtOrderCustomAttributesNames.ShippingAddress.LATITUDE).Value != null
                        && Double.TryParse(order.ShippingAddress.Custom.Fields.FirstOrDefault(cf => cf.Key == CtOrderCustomAttributesNames.ShippingAddress.LATITUDE).Value.ToString(), out currentLat)
                        && currentLat != latitude
                    )
                )
            {
                actions.Add(new OrderSetShippingAddressCustomFieldAction
                {
                    Name = CtOrderCustomAttributesNames.ShippingAddress.LATITUDE,
                    Value = latitude
                });
            }
            if (!order.ShippingAddress.Custom.Fields.Any(cf => cf.Key == CtOrderCustomAttributesNames.ShippingAddress.LONGITUDE)
                ||
                    (order.ShippingAddress.Custom.Fields.FirstOrDefault(cf => cf.Key == CtOrderCustomAttributesNames.ShippingAddress.LONGITUDE).Value != null
                        && Double.TryParse(order.ShippingAddress.Custom.Fields.FirstOrDefault(cf => cf.Key == CtOrderCustomAttributesNames.ShippingAddress.LONGITUDE).Value.ToString(), out currentLong)
                        && currentLong != longitude
                    )
                )
            {
                actions.Add(new OrderSetShippingAddressCustomFieldAction
                {
                    Name = CtOrderCustomAttributesNames.ShippingAddress.LONGITUDE,
                    Value = longitude
                });
            }
            return await CallOrderUpdate(order, actions);
        }
        public async Task<IOrder> UpdateOrderExternalDeliveryFields(IOrder order, DateTime? externalDeliveryScheduledDate, bool externalDeliveryServiceRequested, string externalDeliveryService, string externalDeliveryStatus, decimal externalDeliveryCost, string externalDeliveryTrackingCode)
        {

            var actions = new List<IOrderUpdateAction>();
            actions.Add(new OrderSetCustomFieldAction
            {
                Name = CtOrderCustomAttributesNames.Order.DELIVERY_SCHEDULED_DATE,
                Value = externalDeliveryScheduledDate
            });
            actions.Add(new OrderSetCustomFieldAction
            {
                Name = CtOrderCustomAttributesNames.Order.DELIVERY_SERVICE_REQUESTED,
                Value = externalDeliveryServiceRequested
            });
            actions.Add(new OrderSetCustomFieldAction
            {
                Name = CtOrderCustomAttributesNames.Order.DELIVERY_SERVICE,
                Value = externalDeliveryService
            });
            actions.Add(new OrderSetCustomFieldAction
            {
                Name = CtOrderCustomAttributesNames.Order.DELIVERY_STATUS,
                Value = externalDeliveryStatus
            });
            actions.Add(new OrderSetCustomFieldAction
            {
                Name = CtOrderCustomAttributesNames.Order.DELIVERY_COST,
                Value = new CentPrecisionMoney { CentAmount = (long)(externalDeliveryCost * 100), CurrencyCode = "EUR", FractionDigits = 2 }
            });
            if (!string.IsNullOrWhiteSpace(externalDeliveryTrackingCode))
            {
                actions.Add(new OrderSetCustomFieldAction
                {
                    Name = CtOrderCustomAttributesNames.Order.DELIVERY_TRACKING_CODE,
                    Value = externalDeliveryTrackingCode
                });
            }

            return await CallOrderUpdate(order, actions);
        }

        public async Task<IOrder> UpdateOrderExternalDeliveryStatus(IOrder order, string externalDeliveryStatus)
        {
            var actions = new List<IOrderUpdateAction>();
            actions.Add(new OrderSetCustomFieldAction
            {
                Name = CtOrderCustomAttributesNames.Order.DELIVERY_STATUS,
                Value = externalDeliveryStatus
            });
            return await CallOrderUpdate(order, actions);
        }

        public async Task<bool> IsOrderAlreadyExists(GlobalOrderModel globalOrderModel)
        {
            var formattedTestTime = DateTime.UtcNow.AddSeconds(_commonSettings.CurrentValue.DuplicatedOrderTestTimeSeconds).ToString("yyyy-MM-ddTHH:mm:ssZ");
            var formattedDeliveryDate = globalOrderModel.Shipping.DeliveryDate.ToString("yyyy-MM-dd");

            var query = $"custom(fields(transmitterFloristId=\"{globalOrderModel.SenderFloristIdentifier}\"))" +
                    $" and shippingAddress(custom(fields(date=\"{formattedDeliveryDate}\")))" +
                    $" and shippingAddress(city=\"{globalOrderModel.Shipping.City}\")" +
                    $" and shippingAddress(streetName=\"{globalOrderModel.Shipping.StreetName}\")" +
                    $" and createdAt > \"{formattedTestTime}\"";

            _logger.LogInformation("Query={Query}", query);

            try
            {
                IOrderPagedQueryResponse response = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Orders()
                    .Get()
                    .WithWhere(query)
                    .ExecuteAsync();
                if (response != null && response.Total > 0)
                {
                    long globalOrderTotal = GetGlobalOrderGrossTotal(globalOrderModel.Products);
                    return IsOrderPriceEqual(globalOrderTotal, response.Results);
                }
            }
            catch (commercetools.Base.Client.Error.NotFoundException)
            {
                // No duplicated orders found
            }
            catch (Exception nfex)
            {
                _logger.LogError(nfex, "Error while searching possible duplicated orders with SenderFloristIdentifier {SenderId} and delviery date = {DeliveryDate} because of {Message} - {StackTrace}"
                    , globalOrderModel.SenderFloristIdentifier, globalOrderModel.Shipping.DeliveryDate, nfex.Message, nfex.StackTrace);
            }

            return false;
        }

        private static long GetGlobalOrderGrossTotal(IList<GlobalOrderProduct> globalOrderProducts)
        {
            decimal total = 0;
            foreach (GlobalOrderProduct product in globalOrderProducts)
            {
                total += product.Price;
            }

            return Decimal.ToInt64(total * 100);
        }

        private static long GetCTOrderGrossTotalPrice(IOrder order)
        {
            long total = 0;
            foreach (ILineItem item in order.LineItems)
            {
                total += item.Price.Value.CentAmount;
            }

            return total;
        }

        private static Boolean IsOrderPriceEqual(long orderToCheckPrice, IList<IOrder> searchResult)
        {
            var result = false;
            foreach (IOrder order in searchResult)
            {
                long ctOrderTotal = GetCTOrderGrossTotalPrice(order);
                if (ctOrderTotal == orderToCheckPrice)
                {
                    result = true;
                }
            }
            return result;
        }

        private async Task<IOrder> UpdateOrderRecipientPhoneNumber(IOrder order, string phoneNumber)
        {
            var actions = new List<IOrderUpdateAction>();
            if (order.ShippingAddress.Mobile != phoneNumber)
            {
                actions.Add(new OrderSetShippingAddressAction
                {
                    //Address = new BaseAddress
                    Address = new Address
                    {
                        City = order?.ShippingAddress?.City,
                        PostalCode = order?.ShippingAddress?.PostalCode,
                        StreetName = order?.ShippingAddress?.StreetName,
                        Country = order?.ShippingAddress?.Country, // if the delivery country is changed the order can't be delivered as it is, but it must be cancelled and a new one created
                        FirstName = order.ShippingAddress?.FirstName,
                        LastName = order.ShippingAddress?.LastName,
                        AdditionalAddressInfo = order.ShippingAddress?.AdditionalAddressInfo,
                        AdditionalStreetInfo = order.ShippingAddress?.AdditionalStreetInfo,
                        Apartment = order?.ShippingAddress?.Apartment,
                        Building = order.ShippingAddress?.Building,
                        Company = order?.ShippingAddress?.Company,
                        Email = order?.ShippingAddress?.Email,
                        Mobile = phoneNumber,
                        Phone = order?.ShippingAddress?.Phone,
                        Department = order?.ShippingAddress?.Department,
                        POBox = order?.ShippingAddress?.POBox,
                        Region = order?.ShippingAddress?.Region,
                        Salutation = order?.ShippingAddress?.Salutation,
                        State = order?.ShippingAddress?.State,
                        StreetNumber = order?.ShippingAddress?.StreetNumber,
                        Title = order?.ShippingAddress?.Title,
                        Custom = order?.ShippingAddress?.Custom,
                    }
                });
            }
            return await CallOrderUpdate(order, actions);
        }
        private async Task<IOrder> UpdateOrderRecipientName(IOrder order, string recipientFirstName, string recipientLastName)
        {
            var actions = new List<IOrderUpdateAction>();
            if (order.ShippingAddress.FirstName != recipientFirstName || order.ShippingAddress.LastName != recipientLastName)
            {
                actions.Add(new OrderSetShippingAddressAction
                {
                    //Address = new BaseAddress
                    Address = new Address
                    {
                        City = order?.ShippingAddress?.City,
                        PostalCode = order?.ShippingAddress?.PostalCode,
                        StreetName = order?.ShippingAddress?.StreetName,
                        Country = order?.ShippingAddress?.Country, // if the delivery country is changed the order can't be delivered as it is, but it must be cancelled and a new one created
                        FirstName = recipientFirstName,
                        LastName = recipientLastName,
                        AdditionalAddressInfo = order.ShippingAddress?.AdditionalAddressInfo,
                        AdditionalStreetInfo = order.ShippingAddress?.AdditionalStreetInfo,
                        Apartment = order?.ShippingAddress?.Apartment,
                        Building = order.ShippingAddress?.Building,
                        Company = order?.ShippingAddress?.Company,
                        Email = order?.ShippingAddress?.Email,
                        Mobile = order?.ShippingAddress?.Mobile,
                        Phone = order?.ShippingAddress?.Phone,
                        Department = order?.ShippingAddress?.Department,
                        POBox = order?.ShippingAddress?.POBox,
                        Region = order?.ShippingAddress?.Region,
                        Salutation = order?.ShippingAddress?.Salutation,
                        State = order?.ShippingAddress?.State,
                        StreetNumber = order?.ShippingAddress?.StreetNumber,
                        Title = order?.ShippingAddress?.Title,
                        Custom = order?.ShippingAddress?.Custom
                    }
                });
            }
            return await CallOrderUpdate(order, actions);
        }

        private async Task<IOrder> UpdateOrderRecipientCoordinates(IOrder order, double latitude, double longitude)
        {
            var actions = new List<IOrderUpdateAction>();

            actions.Add(new OrderSetShippingAddressCustomFieldAction
            {
                Name = CtOrderCustomAttributesNames.ShippingAddress.LATITUDE,
                Value = latitude
            });
            actions.Add(new OrderSetShippingAddressCustomFieldAction
            {
                Name = CtOrderCustomAttributesNames.ShippingAddress.LONGITUDE,
                Value = longitude
            });
            return await CallOrderUpdate(order, actions);
        }

        private async Task<IOrder> UpdateOrderNotes(IOrder order, string notes)
        {
            var actions = new List<IOrderUpdateAction>();

            actions.Add(new OrderSetShippingAddressCustomFieldAction
            {
                Name = CtOrderCustomAttributesNames.ShippingAddress.COMMENTS,
                Value = notes
            });
            return await CallOrderUpdate(order, actions);
        }
        private async Task<IOrder> UpdateOrderCardMessage(IOrder order, string cardMessage)
        {
            var actions = new List<IOrderUpdateAction>();
            actions.Add(new OrderSetCustomFieldAction
            {
                Name = CtOrderCustomAttributesNames.Order.MESSAGE,
                Value = cardMessage
            });
            return await CallOrderUpdate(order, actions);
        }
        private async Task<IOrder> UpdateOrderDeliveryDate(IOrder order, DateTime date)
        {
            var actions = new List<IOrderUpdateAction>
            {
                new OrderSetShippingAddressCustomFieldAction
                {
                    Name = CtOrderCustomAttributesNames.ShippingAddress.DATE,
                    Value = date.ToString("yyyy-MM-dd")
                }
            };
            return await CallOrderUpdate(order, actions);
        }

        private async Task<CustomLineItemDraft> GetCustomLineItemForForeignOrder(GlobalOrderProduct product, string currencyCode, string deliveryContryCode, string src, string orderNumber)
        {
            if (product == null)
            {
                throw new CtCustomException("GlobalOrderProduct cannot be null");
            }

            CustomLineItemDraft item = new CustomLineItemDraft();
            item.Custom = new CustomFieldsDraft
            {
                Fields = new FieldContainer(),
                Type = new TypeResourceIdentifier
                {
                    Key = "custom-line-item-custom"
                }
            };

            // FR CASE
            if (orderNumber?.StartsWith("IF") ?? false)
            {
                item.Custom.Fields.Add(CtOrderCustomAttributesNames.CustomLinetItem.SKU, product.ProductKey);
            }
            else
            {
                item.Custom.Fields.Add(CtOrderCustomAttributesNames.CustomLinetItem.SKU, product.ProductKey + "#" + (product.VariantKey != null ? product.VariantKey : ""));
            }

            item.Custom.Fields.Add(CtOrderCustomAttributesNames.CustomLinetItem.DESCRIPTION, "");

            long customMarketingFee = 0;
            if (!String.IsNullOrWhiteSpace(src) && src.ToUpper() == "PFS" &&
                (_commonSettings?.CurrentValue?.MarketingFeeToBeSettedOnProductForInternationalOutbound ?? 0) > 0)
            {
                //customMarketingFee = (long)(_commonSettings.CurrentValue.MarketingFeeToBeSettedOnProductForInternationalOutbound * 100);
                item.Custom.Fields.Add(CtOrderCustomAttributesNames.CustomLinetItem.MARKETING_FEE, new Money
                {
                    CurrencyCode = currencyCode,
                    CentAmount = (long)(_commonSettings.CurrentValue.MarketingFeeToBeSettedOnProductForInternationalOutbound * 100)
                });
            }
            else
            {
                if (product.MarketingFee.HasValue)
                {
                    item.Custom.Fields.Add(CtOrderCustomAttributesNames.CustomLinetItem.MARKETING_FEE, new Money
                    {
                        CurrencyCode = currencyCode,
                        CentAmount = (long)(product.MarketingFee * 100)
                    });
                }
            }

            decimal foreignOrderTaxation = 0;
            long productPriceInCent = (long)(product.Price * 100) + customMarketingFee;
            item.Quantity = product.Quantity;
            item.Name = new LocalizedString();
            item.Money = new Money
            {
                CurrencyCode = currencyCode,
                CentAmount = productPriceInCent
            };
            item.ExternalTaxRate = new ExternalTaxRateDraft { Amount = productPriceInCent * foreignOrderTaxation / 100, IncludedInPrice = true, Country = deliveryContryCode, Name = "Custom taxation intl orders" };

            if (!string.IsNullOrWhiteSpace(deliveryContryCode) && !string.IsNullOrWhiteSpace(product.ProductKey) &&
                product.ProductKey.StartsWith(deliveryContryCode + "-"))
            {
                item.Slug = (!string.IsNullOrWhiteSpace(product.ProductKey) ? product.ProductKey : string.Empty) + "#" +
                    (!string.IsNullOrWhiteSpace(product.VariantKey) ? product.VariantKey : string.Empty);
            }
            else
            {
                item.Slug = (!string.IsNullOrWhiteSpace(deliveryContryCode) ? deliveryContryCode : string.Empty) + "-" +
                    (!string.IsNullOrWhiteSpace(product.ProductKey) ? product.ProductKey : string.Empty) + "#" +
                    (!string.IsNullOrWhiteSpace(product.VariantKey) ? product.VariantKey : string.Empty);
            }

            item.Name[_commonSettings.CurrentValue.LocalCountryCode] = product.Name;


            return item;
        }
        private async Task<GetLineItemForLocalOrder_Result> GetLineItemForLocalOrder(GlobalOrderProduct product, string currencyCode = null)
        {
            if (product == null)
            {
                throw new CtCustomException("GlobalOrderProduct cannot be null");
            }
            string key = product.ProductKey;
            if (product.IsBundlePart())
                key = product.BundleId.Split('-').FirstOrDefault() ?? key;

            GetLineItemForLocalOrder_Result result = new()
            {
                Product = await _productService.GetByKey(key , ["taxCategory"])
            };

            if (result.Product == null)
                throw new CtCustomException($"Cannot find product with key {product.ProductKey}");

            IProductVariant? variant = GetProductVariant(product, result.Product);

            result.LineItem = new LineItemDraft
            {
                Quantity = product.ExtractProductSizeFromVariantKey() >= STEM_SIZE ? product.ExtractProductSizeFromVariantKey() ?? 1 : product.Quantity, // for A la tige product we need to set the quantity to the size of the product
                ProductId = result.Product.Id,
                VariantId = variant.Id,
                DistributionChannel = new ChannelResourceIdentifier { Id = _ctChannelId },
                Custom = new CustomFieldsDraft
                {
                    Fields = new FieldContainer(),
                    Type = new TypeResourceIdentifier
                    {
                        Key = "line-item-custom"
                    }
                }
            };

            SetProductExternalPrice(product, result.Product, variant);
            if (product.IsExternalPrice)//TODO in future check difference price bewtween incoming order and CT || product.Price != variant.Price.Value.CentAmount / (decimal)Math.Pow(10, variant.Price.Value.FractionDigits))
            {
                result.LineItem.ExternalPrice = new Money { CentAmount = (long)(product.Price * 100), CurrencyCode = "EUR" };
                _logger.LogInformation("ExternalPrice set to {Price}", product.Price);
            }

            SetMarketingFee(product.MarketingFee, currencyCode, result);
            SetRibbonText(product.RibbonText, result);
            SetIsAccessoryFor(product.IsAccessoryFor, result);
            SetComposition(product.Composition, result);
            SetExecutingFloristAmount(product.ExecutingFloristAmount, currencyCode, result);

            return result;
        }

        

        private static void SetIsAccessoryFor(string isAccessoryFor, GetLineItemForLocalOrder_Result result)
        {
            if (!String.IsNullOrWhiteSpace(isAccessoryFor))
            {
                EnsureLineItemCustom(result);

                result.LineItem.Custom.Fields.Add(CtOrderCustomAttributesNames.LineItem.IS_ACCESSORY_FOR, (isAccessoryFor));
            }
        }

        private static void SetRibbonText(string ribbonText, GetLineItemForLocalOrder_Result result)
        {
            if (!String.IsNullOrWhiteSpace(ribbonText))
            {
                EnsureLineItemCustom(result);
                result.LineItem.Custom.Fields.Add(CtOrderCustomAttributesNames.LineItem.RIBBON_TEXT, (ribbonText));
            }
        }

        private static void SetComposition(string? composition, GetLineItemForLocalOrder_Result result)
        {
            if (!String.IsNullOrWhiteSpace(composition))
            {
                EnsureLineItemCustom(result);
                result.LineItem.Custom.Fields.Add(CtOrderCustomAttributesNames.LineItem.COMPOSITION, (composition));
            }
        }

        private static void SetMarketingFee(decimal? marketingFee, string currencyCode, GetLineItemForLocalOrder_Result result)
        {
            if (marketingFee.HasValue)
            {
                EnsureLineItemCustom(result);

                result.LineItem.Custom.Fields.Add(CtOrderCustomAttributesNames.CustomLinetItem.MARKETING_FEE, new Money
                {
                    CurrencyCode = currencyCode,
                    CentAmount = (long)(marketingFee * 100)
                });
            }
        }
        private static void SetExecutingFloristAmount(decimal? executingFloristAmount, string currencyCode, GetLineItemForLocalOrder_Result result)
        {
            if (executingFloristAmount.HasValue)
            {
                EnsureLineItemCustom(result);

                result.LineItem.Custom.Fields.Add(CtOrderCustomAttributesNames.LineItem.EXECUTING_FLORIST_AMOUNT, new Money
                {
                    CurrencyCode = currencyCode,
                    CentAmount = (long)(executingFloristAmount * 100)
                });
            }
        }


        private static void EnsureLineItemCustom(GetLineItemForLocalOrder_Result result)
        {
            if (result.LineItem.Custom == null)
            {
                result.LineItem.Custom = new CustomFieldsDraft
                {
                    Fields = new FieldContainer(),
                    Type = new TypeResourceIdentifier
                    {
                        Key = "line-item-custom"
                    }
                };
            }
        }

        public async Task<IOrder> CallOrderUpdate(IOrder order, List<IOrderUpdateAction> actions)
        {
            IOrder updatedOrder = null;
            if (actions.Count > 0)
            {

                OrderUpdate update = new OrderUpdate
                {
                    Version = order.Version,
                    Actions = actions
                };
                try
                {
                    updatedOrder = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                        .Orders()
                        .WithId(order.Id)
                        .Post(update)
                        .ExecuteAsync();
                }
                catch (BadRequestException ex)
                {
                    _logger.LogError(ex, $"Error while updating order with id {order.Id}, body = {ex.Body} because of {ex.Message} - {ex.StackTrace}");
                    throw;
                }
                catch (NotFoundException nfex)
                {
                    _logger.LogError(nfex, $"Error 404 while updating order with id {order.Id} because of {nfex.Message} - {nfex.StackTrace}");
                    throw;
                }
                catch (ConcurrentModificationException nfex)
                {
                    _logger.LogError(nfex, $"Error 409 while updating order with id {order.Id} because of {nfex.Message} - {nfex.StackTrace}");
                    throw;
                }

                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error while updating order with id {order.Id} because of {ex.Message} - {ex.StackTrace}");
                    updatedOrder = null;
                }
            }
            return updatedOrder;
        }

        private async Task<IOrderEdit> CreateOrderEditDraft(string orderId)
        {
            OrderEditDraft draft = new OrderEditDraft
            {
                Resource = new OrderReference { Id = orderId },
                StagedActions = new List<IStagedOrderUpdateAction>()
            };

            IOrderEdit orderEditCreateResult = null;
            try
            {
                orderEditCreateResult = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Orders().Edits().Post(draft).ExecuteAsync();
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, $"Error 404 while retrieving order with id {orderId}");
            }
            catch (commercetools.Base.Client.Error.UnauthorizedException nfex)
            {
                _logger.LogError(nfex, $"Error 401 while retrieving order with id {orderId} - {nfex.Body}");
            }
            catch (Exception nfex)
            {
                _logger.LogError(nfex, $"Error while retrieving order with id {orderId} because of {nfex.Message} - {nfex.StackTrace}");
            }

            if (orderEditCreateResult == null)
            {
                throw new CtCustomException($"The order edit create was not created for order id {orderId}");
            }
            return orderEditCreateResult;
        }
        private async Task UpdateOrderLineItems(IOrder order, List<IOrderEditUpdateAction> actions)
        {
            var orderEditCreateResult = await CreateOrderEditDraft(order.Id);
            OrderEditUpdate orderEditUpdate = new OrderEditUpdate();
            orderEditUpdate.Version = orderEditCreateResult.Version;
            orderEditUpdate.Actions = actions;
            await PostOrderEditUpdate(order, orderEditUpdate, orderEditCreateResult);
        }

        private async Task PostOrderEditUpdate(IOrder order, OrderEditUpdate orderEditUpdate, IOrderEdit orderEditCreateResult)
        {
            IOrderEdit orderEditResult = null;
            try
            {
                orderEditResult = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Orders().Edits().WithId(orderEditCreateResult.Id).Post(orderEditUpdate).ExecuteAsync();
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, "Error 404 while retrieving order with id {orderId}", order.Id);
            }
            catch (commercetools.Base.Client.Error.UnauthorizedException nfex)
            {
                _logger.LogError(nfex, "Error 401 while retrieving order with id {orderId} - {nfex}", order.Id, nfex.ToString());
            }
            catch (commercetools.Base.Client.Error.ConcurrentModificationException nfex)
            {
                _logger.LogError(nfex, "Error 409 while retrieving order with id {orderId} - {nfex}", order.Id, nfex.ToString());
            }
            catch (Exception nfex)
            {
                _logger.LogError(nfex, "Error while retrieving order with id {orderId} because of {nfex}", order.Id, nfex.ToString());
            }

            if (orderEditResult == null)
            {
                await SafeDeleteOrderEdit(orderEditCreateResult.Id, orderEditCreateResult.Version, orderEditCreateResult.Id, order.Id);
                throw new CtCustomException($"Cannot create orderEditResult object null with orderEditCreateResult.Id {orderEditCreateResult.Id} for order.id {order.Id}");
            }
            else if (orderEditResult.Result.Type != "PreviewSuccess")
            {
                await SafeDeleteOrderEdit(orderEditResult.Id, orderEditResult.Version, orderEditCreateResult.Id, order.Id);
                throw new CtCustomException($"Cannot create orderEditResult result type {orderEditResult.Result.Type} with orderEditCreateResult.Id  {orderEditCreateResult.Id} for order.id {order.Id}");
            }

            OrderEditApply orderEditApply = new OrderEditApply
            {
                EditVersion = (int)orderEditResult.Version,
                ResourceVersion = (int)order.Version
            };

            IOrderEdit orderEditApplyResult = null;
            try
            {
                orderEditApplyResult = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Orders().Edits().WithId(orderEditCreateResult.Id).Apply().Post(orderEditApply).ExecuteAsync();
            }
            catch (commercetools.Base.Client.Error.NotFoundException nfex)
            {
                _logger.LogError(nfex, "Error 404 while retrieving order with id {orderId}", order.Id);
            }
            catch (commercetools.Base.Client.Error.UnauthorizedException nfex)
            {
                _logger.LogError(nfex, "Error 401 while retrieving order with id {orderId} - {nfex}", order.Id, nfex.ToString());
            }
            catch (Exception nfex)
            {
                _logger.LogError(nfex, "Error while retrieving order with id {orderId} because of {nfex}", order.Id, nfex.ToString());
            }

            if (orderEditApplyResult == null)
            {
                await SafeDeleteOrderEdit(orderEditResult.Id, orderEditResult.Version, orderEditCreateResult.Id, order.Id);
                throw new CtCustomException($"Cannot create orderEditApplyResult object null with orderEditCreateResult.Id {orderEditCreateResult.Id} for order.id {order.Id}");
            }
            if (orderEditApplyResult.Result.Type != "Applied")
            {
                await SafeDeleteOrderEdit(orderEditApplyResult.Id, orderEditApplyResult.Version, orderEditCreateResult.Id, order.Id);
                throw new CtCustomException($"Cannot create orderEditResult result type {orderEditResult.Result.Type} with orderEditCreateResult.Id  {orderEditCreateResult.Id} for order.id {order.Id}");
            }
            await SafeDeleteOrderEdit(orderEditApplyResult.Id, orderEditApplyResult.Version, orderEditCreateResult.Id, order.Id);
        }

        /// <summary>
        /// Safely deletes an order edit with defensive error handling to prevent disrupting the main flow
        /// </summary>
        /// <param name="orderEditId">The ID of the order edit to delete</param>
        /// <param name="version">The version of the order edit</param>
        /// <param name="originalOrderEditId">The original order edit ID for logging purposes</param>
        /// <param name="orderId">The order ID for logging purposes</param>
        private async Task SafeDeleteOrderEdit(string orderEditId, long version, string originalOrderEditId, string orderId)
        {
            try
            {
                var orderEditDeleted = await _commerceToolsClient.WithApi().WithProjectKey(_projectKey)
                    .Orders().Edits().WithId(orderEditId).Delete().WithVersion(version).ExecuteAsync();
                if (orderEditDeleted == null)
                {
                    _logger.LogWarning("Order edit with id {orderEditCreateResultId} was not deleted after applying changes to order {orderId} for Version: {version}",
                        originalOrderEditId, orderId, version);
                }
                else
                {
                    _logger.LogInformation("Order edit with id {orderEditCreateResultId} was deleted after applying changes to order {orderId} for Version: {version}",
                        originalOrderEditId, orderId, version);
                }
            }
            catch (Exception ex)
            {
                // Defensive catch - log the error but don't throw to avoid disrupting the main flow
                _logger.LogWarning(ex, "Failed to delete order edit with id {orderEditId} for order {orderId}. This will not affect the main operation.",
                    originalOrderEditId, orderId);
            }
        }

        private async Task<IOrder> UpdateOrderItem(IOrder order, GlobalOrderItemUpdated orderItemUpdated)
        {
            //var currentFirstLineItem = order.LineItems.FirstOrDefault();

            // 1 - create order edit
            var orderEditCreateResult = await CreateOrderEditDraft(order.Id);


            // 2 
            OrderEditUpdate orderEditUpdate = new OrderEditUpdate();
            orderEditUpdate.Version = orderEditCreateResult.Version;
            orderEditUpdate.Actions = new List<IOrderEditUpdateAction>();

            // ADD 
            if (orderItemUpdated.Type == OrderItemTypeEnum.NEW)
            {
                if (order.ShippingAddress.Country == _commonSettings.CurrentValue.LocalCountryCode)
                {
                    StagedOrderAddLineItemAction stagedOrderAddLineItemAction = await GetStagedOrderAddLineItemActionLocalOrder(order.TotalPrice.CurrencyCode, orderItemUpdated.Product);

                    if (stagedOrderAddLineItemAction == null)
                    {
                        throw new CtCustomException($"Cannot create StagedOrderAddLineItemAction with key {orderItemUpdated.Product.ProductKey}");
                    }

                    orderEditUpdate.Actions.Add(new OrderEditAddStagedActionAction
                    {
                        StagedAction = stagedOrderAddLineItemAction
                    });
                }
                else
                {
                    string currency_code = order.TotalPrice.CurrencyCode;

                    StagedOrderAddCustomLineItemAction stagedOrderAddCustomLineItemAction = GetStagedOrderAddCustomLineItemActionForeignOrder(currency_code, orderItemUpdated);

                    orderEditUpdate.Actions.Add(new OrderEditAddStagedActionAction
                    {
                        StagedAction = stagedOrderAddCustomLineItemAction
                    });
                }
            }
            else if (orderItemUpdated.Type == OrderItemTypeEnum.DELETE) // DELETE
            {
                if (order.ShippingAddress.Country == _commonSettings.CurrentValue.LocalCountryCode)
                {
                    foreach (var lineItem in order.LineItems)
                    {
                        if (lineItem.ProductKey == orderItemUpdated.Product.ProductKey && lineItem.Variant.Key == orderItemUpdated.Product.VariantKey ||
                             (lineItem.ProductKey == orderItemUpdated.Product.ProductKey && orderItemUpdated.Product.VariantKey.Contains("-PL-")))
                        {
                            orderEditUpdate.Actions.Add(new OrderEditAddStagedActionAction
                            {
                                StagedAction = new StagedOrderRemoveLineItemAction
                                {
                                    LineItemId = lineItem.Id
                                }
                            });
                            break;
                        }
                    }
                }
                else
                {
                    string skuProduct = orderItemUpdated.Product.ProductKey + "#" + (!string.IsNullOrWhiteSpace(orderItemUpdated.Product.VariantKey) ? orderItemUpdated.Product.VariantKey : "");
                    foreach (var lineItemfo in order.CustomLineItems)
                    {
                        if (lineItemfo.Custom.Fields.ContainsKey(CtOrderCustomAttributesNames.CustomLinetItem.SKU))
                        {
                            string skuCustomLineItem = lineItemfo.Custom.Fields[CtOrderCustomAttributesNames.CustomLinetItem.SKU].ToString();
                            if (skuCustomLineItem.Equals(skuProduct))
                            {
                                orderEditUpdate.Actions.Add(new OrderEditAddStagedActionAction
                                {
                                    StagedAction = new StagedOrderRemoveCustomLineItemAction
                                    {
                                        CustomLineItemId = lineItemfo.Id
                                    }
                                });
                                break;
                            }
                        }
                    }
                }
            }

            await PostOrderEditUpdate(order, orderEditUpdate, orderEditCreateResult);

            var returnOrder = await GetById(orderItemUpdated.OrderIdentifier);

            return returnOrder;
        }

        private GlobalOrderProduct ConvertRaoLineItemToGlobalOrderProduct(ProductInformations raoLineItem)
        {
            var globalOrderProduct = new GlobalOrderProduct
            {
                RibbonText = raoLineItem.RibbonText ?? string.Empty,
                IsAccessoryFor = string.Empty,
                Price = Convert.ToDecimal(raoLineItem.Price),
                ProductKey = raoLineItem.GetProductKey(),
                VariantKey = raoLineItem.GetVariantKey(),
                Quantity = (int.TryParse(raoLineItem.Size, out int sizeValue) && sizeValue >= STEM_SIZE) ? sizeValue : raoLineItem.Quantity,
                Description = raoLineItem.Description ?? string.Empty,
                Name = raoLineItem.Label ?? raoLineItem.ProductId,
                MarketingFee = Convert.ToDecimal(raoLineItem.Margin),
                IsExternalPrice = raoLineItem.Size?.Equals("PL", StringComparison.OrdinalIgnoreCase) ?? false,
                Location = raoLineItem.Style?.ToUpper() ?? string.Empty,
                ExecutingFloristAmount = raoLineItem.GetExecutingFloristAmount(),
                Composition = raoLineItem.Description ?? string.Empty,
                BundleId = raoLineItem.BundleId ?? string.Empty,
            };

            return globalOrderProduct;
        }

        private async Task<StagedOrderAddLineItemAction?> GetStagedOrderAddLineItemActionLocalOrder(string currencyCode, GlobalOrderProduct orderProduct, string? channelId = null)
        {
            string key = orderProduct.ProductKey;
            if (orderProduct.IsBundlePart())
                key = orderProduct.BundleId.Split('-').FirstOrDefault() ?? key;

            IProduct product = await _productService.GetByKey(key);
            if (product == null)
                return null;

            IProductVariant variant = GetProductVariant(orderProduct, product);

            StagedOrderAddLineItemAction stagedOrderAddLineItemAction = new StagedOrderAddLineItemAction
            {
                ProductId = product.Id,
                VariantId = variant.Id,
                Quantity = orderProduct.Quantity,
                DistributionChannel = new ChannelResourceIdentifier { Id = string.IsNullOrEmpty(channelId) ? _ctChannelId : channelId},
            };
            SetProductExternalPrice(orderProduct, product, variant);
            if (orderProduct.IsExternalPrice)
            {
                stagedOrderAddLineItemAction.ExternalPrice = new Money { CentAmount = (long)(orderProduct.Price * 100), CurrencyCode = "EUR" };
                _logger.LogInformation("ExternalPrice set to {Price}", orderProduct.Price);
            }

            if (stagedOrderAddLineItemAction.Custom == null)
            {
                stagedOrderAddLineItemAction.Custom = new CustomFieldsDraft
                {
                    Fields = new FieldContainer(),
                    Type = new TypeResourceIdentifier
                    {
                        Key = "line-item-custom"
                    }
                };
            }
            if (orderProduct.MarketingFee.HasValue)
            {
                stagedOrderAddLineItemAction.Custom.Fields.Add(CtOrderCustomAttributesNames.LineItem.MARKETING_FEE, new Money
                {
                    CurrencyCode = currencyCode,
                    CentAmount = (long)(orderProduct.MarketingFee.Value * 100)
                });
            }
            if (!String.IsNullOrWhiteSpace(orderProduct.RibbonText))
            {
                stagedOrderAddLineItemAction.Custom.Fields.Add(CtOrderCustomAttributesNames.LineItem.RIBBON_TEXT, (orderProduct.RibbonText));
            }
            if (!String.IsNullOrWhiteSpace(orderProduct.IsAccessoryFor))
            {
                stagedOrderAddLineItemAction.Custom.Fields.Add(CtOrderCustomAttributesNames.LineItem.IS_ACCESSORY_FOR, (orderProduct.IsAccessoryFor));
            }
            
            if (orderProduct.ExecutingFloristAmount.HasValue)
            {
                stagedOrderAddLineItemAction.Custom.Fields.Add(CtOrderCustomAttributesNames.LineItem.EXECUTING_FLORIST_AMOUNT, new Money
                {
                    CurrencyCode = currencyCode,
                    CentAmount = (long)(orderProduct.ExecutingFloristAmount.Value * 100)
                });
            }
            if (!String.IsNullOrWhiteSpace(orderProduct.Composition))
            {
                stagedOrderAddLineItemAction.Custom.Fields.Add(CtOrderCustomAttributesNames.LineItem.COMPOSITION, (orderProduct.Composition));
            }

            return stagedOrderAddLineItemAction;
        }


        private StagedOrderAddCustomLineItemAction GetStagedOrderAddCustomLineItemActionForeignOrder(string currencyCode, GlobalOrderItemUpdated orderItemUpdated)
        {
            StagedOrderAddCustomLineItemAction stagedOrderAddCustomLineItemAction = new()
            {
                Quantity = orderItemUpdated.Product.Quantity,
                Name = new LocalizedString(),
                Money = new Money
                {
                    CurrencyCode = currencyCode,
                    CentAmount = (long)(orderItemUpdated.Product.Price * 100)
                }
            };
            stagedOrderAddCustomLineItemAction.Name[_commonSettings.CurrentValue.LocalCountryCode] = orderItemUpdated.Product.Name;

            stagedOrderAddCustomLineItemAction.Custom = new CustomFieldsDraft
            {
                Fields = new FieldContainer(),
                Type = new TypeResourceIdentifier
                {
                    Key = "custom-line-item-custom"
                }
            };
            stagedOrderAddCustomLineItemAction.Custom.Fields.Add(CtOrderCustomAttributesNames.CustomLinetItem.SKU, orderItemUpdated.Product.ProductKey + "#" + (!string.IsNullOrWhiteSpace(orderItemUpdated.Product.VariantKey) ? orderItemUpdated.Product.VariantKey : ""));

            if (orderItemUpdated.Product.MarketingFee.HasValue)
            {
                stagedOrderAddCustomLineItemAction.Custom.Fields.Add(CtOrderCustomAttributesNames.CustomLinetItem.MARKETING_FEE, new Money
                {
                    CurrencyCode = currencyCode,
                    CentAmount = (long)(orderItemUpdated.Product.MarketingFee.Value * 100)
                });
            }

            return stagedOrderAddCustomLineItemAction;
        }

        private void SetProductExternalPrice(GlobalOrderProduct product, IProduct ctProduct, IProductVariant variant)
        {
            if (!variant.HasPriceMatchingProduct(product, product.ExtractProductSizeFromVariantKey() ?? 1))
            {
                _logger.LogWarning("Force externalPrice product for variant : {VariantKey} because no matching price {Price} found in CT", product.VariantKey, product.Price);
                product.IsExternalPrice = true;
            }
            //Check if the product price provided is matching with one wariant price but not available anymore 
            else if (variant.HasExpiredPriceMatchingProduct(product, product.ExtractProductSizeFromVariantKey() ?? 1))
            {
                _logger.LogWarning("Force externalPrice product for variant : {VariantKey} because its availability date in CT is arrived at due date", product.VariantKey);
                product.IsExternalPrice = true;
            }

            // to check if the product in the order have been choose with an external Price match the configuration from CT
            if (product.IsExternalPrice && !ctProduct.GetAllowPriceDifferentFromVariantsOne())
            {
                _logger.LogWarning("Configuration error in CT : custom field that allow external price is set to false for product {ProductKey} when attempt to create lineItem with externalPrice", product.ProductKey);
            }
        }

        private IProductVariant GetProductVariant(GlobalOrderProduct product, IProduct ctProduct)
        {
            IProductVariant? variant = null;
            // 1AFO cases for france --> Roses a la Tige
            if (product.ExtractProductSizeFromVariantKey() >= STEM_SIZE) // check if the size is greater than 6 (only rule that we have right now to detect a "AlaTige" Product)
            {
                // create the variant Key for CT from the VariantKey of the product
                string? variantKey = product.TransfromVariantKeyForCTWithAlaTigeProduct();

                if (variantKey == null)
                {
                    _logger.LogError("Cannot transform the variantKey from the OrderProduct to the CT VariantKey format for Roses With Stem : OrderProduct VariantKey:{VariantKey}", product.VariantKey);
                    variant = ctProduct.MasterData.Current.MasterVariant;
                    product.IsExternalPrice = true;//to force the line item price to be set in the external price
                    _logger.LogError("We will create the order in CT with the default MasterVariant of the product {id} , and with an externalPrice to force the good price event if we are in Roses with stem mode", product.ProductKey);
                }
                else
                {
                    variant = ctProduct.GetMatchingVariant(variantKey);

                    if (variant == null)
                    {
                        _logger.LogWarning("Cannot find variant for product : {VariantKey} mapped into {CtVariantKey} for CT for Roses With Stem product {id}", product.VariantKey, variantKey, product.ProductKey);
                        variant = ctProduct.MasterData.Current.MasterVariant;
                        product.IsExternalPrice = true;//to force the line item price to be set in the external price 
                        _logger.LogError("We will create the order in CT with the default MasterVariant of the product {id} , and with an externalPrice to force the good price event if we are in Roses with stem mode", product.ProductKey);
                    }
                }

            }
            else if (product.IsExternalPrice)
                variant = _productService.GetVariantProductByFreePrice(ctProduct, product.Location, product.Price)!;


            else if (string.IsNullOrEmpty(product.VariantKey) || ctProduct.MasterData.Current.Variants.Count == 1)
            {
                if (ctProduct.MasterData.Current.Variants.Count > 0 && product.VariantKey != ctProduct.MasterData.Current.MasterVariant.Key)
                {
                    _logger.LogWarning("Take the first Variants because product {VariantKey} no match with masterVariant", product.VariantKey);
                    variant = ctProduct.MasterData.Current.Variants.FirstOrDefault()!;
                }


                if (variant == null)
                {
                    _logger.LogWarning("Cannot find variant for product : {ProductKey} [1]", product.ProductKey);
                    variant = ctProduct.MasterData.Current.MasterVariant;
                    product.IsExternalPrice = true;//to force the line item price to be set in the external price 
                }
            }
            else
            {
                if (ctProduct.MasterData.Current.Variants.Count > 0 && product.VariantKey != ctProduct.MasterData.Current.MasterVariant.Key)
                {
                    variant = ctProduct.MasterData.Current.Variants.FirstOrDefault(pv => pv.Key == product.VariantKey)!;
                }
                else if (ctProduct.MasterData.Current.Variants.Count == 0 && product.VariantKey == ctProduct.MasterData.Current.MasterVariant.Key)
                {
                    variant = ctProduct.MasterData.Current.MasterVariant;
                }

                if (variant == null)
                {
                    _logger.LogWarning("Cannot find variant for product : {VariantKey} [2]", product.VariantKey);
                    variant = ctProduct.MasterData.Current.MasterVariant;
                    product.IsExternalPrice = true;//to force the line item price to be set in the external price 
                }
            }
            return variant;
        }

        #region RAO France Private CT Update

        private string GetCtStatusFromFrRAOStatus(string raoStatus) => raoStatus switch
        {
            "ATT" => StatusHelper.GetStringValue(StatusEnum.NEW_ORDER),
            "ATF" => StatusHelper.GetStringValue(StatusEnum.IN_PROGRESS),
            "DA" => StatusHelper.GetStringValue(StatusEnum.ABSENT),
            "AFF" => StatusHelper.GetStringValue(StatusEnum.ACCEPTED),
            "ATM" => StatusHelper.GetStringValue(StatusEnum.ASSIGNATION_NOT_POSSIBLE),
            "ATC" => StatusHelper.GetStringValue(StatusEnum.ASSIGNATION_NOT_POSSIBLE),
            "NPA" => StatusHelper.GetStringValue(StatusEnum.ASSIGNATION_NOT_POSSIBLE),
            "LFA" => StatusHelper.GetStringValue(StatusEnum.DELIVERED),
            "LFM" => StatusHelper.GetStringValue(StatusEnum.DELIVERED),
            "ANN" => StatusHelper.GetStringValue(StatusEnum.CANCELLED),
            "ANR" => StatusHelper.GetStringValue(StatusEnum.CANCELLED),
            _ => StatusHelper.GetStringValue(StatusEnum.NEW_ORDER),
        };

        public OrderUpdate CreateOrderUpdate(IOrder order, List<KeyValuePair<OrderDifference, object>> fieldsUpdated)
        {
            var orderUpdate = new OrderUpdate();
            orderUpdate.Version = order.Version;

            orderUpdate.Actions = [];

            foreach (var field in fieldsUpdated)
            {
                switch (field.Key)
                {
                    case OrderDifference.Status:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetCustomFieldAction()
                            {
                                Name = CtOrderCustomAttributesNames.Order.FLORIST_ORDER_STATUS,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.SalesOrigin:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetCustomFieldAction()
                            {
                                Name = CtOrderCustomAttributesNames.Order.DEVICE,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.OriginSystem:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetCustomFieldAction()
                            {
                                Name = CtOrderCustomAttributesNames.Order.ORDER_SOURCE,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.CreationDate:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetCustomFieldAction()
                            {
                                Name = CtOrderCustomAttributesNames.Order.ORDER_CREATION_DATE,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.CreationDateTimeStamp:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetCustomFieldAction()
                            {
                                Name = CtOrderCustomAttributesNames.Order.ORDER_CREATION_DATE_TIMESTAMP,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.DeliveryDate:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetShippingAddressCustomFieldAction()
                            {
                                Name = CtOrderCustomAttributesNames.ShippingAddress.DATE,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.DeliveryMoment:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetShippingAddressCustomFieldAction()
                            {
                                Name = CtOrderCustomAttributesNames.ShippingAddress.MOMENT,
                                Value = int.TryParse(field.Value.ToString(), out int value)
                                    ? (Enum.IsDefined(typeof(MomentEnum), value) ? value.ToString(): ((int)MomentEnum.Wholeday).ToString()) 
                                    : ((int)MomentEnum.Wholeday).ToString()

                            }
                        );
                        break;

                    case OrderDifference.DeliveryTime:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetShippingAddressCustomFieldAction()
                            {
                                Name = CtOrderCustomAttributesNames.ShippingAddress.TIME,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.DeliveryInstructions:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetShippingAddressCustomFieldAction
                            {
                                Name = CtOrderCustomAttributesNames.ShippingAddress.COMMENTS,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.DeliveryMode:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetCustomFieldAction
                            {
                                Name = CtOrderCustomAttributesNames.Order.DELIVERY_MODE,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.ExecutingFloristId:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetCustomFieldAction
                            {
                                Name = CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_ID,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.TransmitterFloristId:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetCustomFieldAction
                            {
                                Name = CtOrderCustomAttributesNames.Order.TRANSMITTER_FLORIST_ID,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.CustomerFirstName:
                    case OrderDifference.CustomerLastName:
                    case OrderDifference.CustomerEmail:
                    case OrderDifference.BillingStreet:
                    case OrderDifference.BillingCity:
                    case OrderDifference.BillingZipCode:
                    case OrderDifference.CustomerPhoneNumber:
                    case OrderDifference.CustomerCompany:
                        if (!orderUpdate.Actions.OfType<OrderSetBillingAddressAction>().Any())
                        {
                            orderUpdate.Actions.Add
                            (
                                new OrderSetBillingAddressAction
                                {
                                    Address = new commercetools.Sdk.Api.Models.Common.Address
                                    {
                                        City = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.BillingCity).Value?.ToString() ?? order?.BillingAddress?.City,
                                        PostalCode = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.BillingZipCode).Value?.ToString() ?? order?.BillingAddress?.PostalCode,
                                        StreetName = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.BillingStreet).Value?.ToString() ?? order?.BillingAddress?.StreetName,
                                        Country = order?.BillingAddress?.Country,
                                        FirstName = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.CustomerFirstName).Value?.ToString() ?? order?.BillingAddress?.FirstName,
                                        LastName = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.CustomerLastName).Value?.ToString() ?? order?.BillingAddress?.LastName,
                                        AdditionalAddressInfo = order?.BillingAddress?.AdditionalAddressInfo,
                                        AdditionalStreetInfo = order?.BillingAddress?.AdditionalStreetInfo,
                                        Apartment = order?.BillingAddress?.Apartment,
                                        Building = order?.BillingAddress?.Building,
                                        Company = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.CustomerCompany).Value?.ToString() ?? order?.BillingAddress?.Company,
                                        Email = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.CustomerEmail).Value?.ToString() ?? order?.BillingAddress?.Email,
                                        Mobile = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.CustomerPhoneNumber).Value?.ToString() ?? order?.BillingAddress?.Mobile,
                                        Phone = order?.BillingAddress?.Phone,
                                        Department = order?.BillingAddress?.Department,
                                        POBox = order?.BillingAddress?.POBox,
                                        Region = order?.BillingAddress?.Region,
                                        Salutation = order?.BillingAddress?.Salutation,
                                        State = order?.BillingAddress?.State,
                                        StreetNumber = order?.BillingAddress?.StreetNumber,
                                        Title = order?.BillingAddress?.Title,
                                        Custom = order?.BillingAddress?.Custom
                                    }
                                }
                            );
                        }
                        break;

                    case OrderDifference.RecipientFirstName:
                    case OrderDifference.RecipientLastName:
                    case OrderDifference.RecipientPhoneNumber:
                    case OrderDifference.DeliveryCompany:
                    case OrderDifference.DeliveryStreet:
                    case OrderDifference.DeliveryCity:
                    case OrderDifference.DeliveryZipCode:
                    case OrderDifference.DeliveryCountryCode:
                    case OrderDifference.DeliveryAdditionalInfo:
                        if (!orderUpdate.Actions.OfType<OrderSetShippingAddressAction>().Any())
                        {
                            orderUpdate.Actions.Add
                            (
                                new OrderSetShippingAddressAction
                                {
                                    Address = new Address
                                    {
                                        City = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.DeliveryCity).Value?.ToString() ?? order?.ShippingAddress?.City,
                                        PostalCode = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.DeliveryZipCode).Value?.ToString() ?? order?.ShippingAddress?.PostalCode,
                                        StreetName = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.DeliveryStreet).Value?.ToString() ?? order?.ShippingAddress?.StreetName,
                                        Country = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.DeliveryCountryCode).Value?.ToString() ?? order?.ShippingAddress?.Country,
                                        FirstName = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.RecipientFirstName).Value?.ToString() ?? order?.ShippingAddress?.FirstName,
                                        LastName = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.RecipientLastName).Value?.ToString() ?? order?.ShippingAddress?.LastName,
                                        AdditionalAddressInfo = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.DeliveryAdditionalInfo).Value?.ToString() ?? order?.ShippingAddress?.AdditionalAddressInfo,
                                        AdditionalStreetInfo = order?.ShippingAddress?.AdditionalStreetInfo,
                                        Apartment = order?.ShippingAddress?.Apartment,
                                        Building = order?.ShippingAddress?.Building,
                                        Company = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.DeliveryCompany).Value?.ToString() ?? order?.ShippingAddress?.Company,
                                        Email = order?.ShippingAddress?.Email,
                                        Mobile = order?.ShippingAddress?.Mobile,
                                        Phone = fieldsUpdated?.FirstOrDefault(pair => pair.Key == OrderDifference.RecipientPhoneNumber).Value?.ToString() ?? order?.ShippingAddress?.Phone,
                                        Department = order?.ShippingAddress?.Department,
                                        POBox = order?.ShippingAddress?.POBox,
                                        Region = order?.ShippingAddress?.Region,
                                        Salutation = order?.ShippingAddress?.Salutation,
                                        State = order?.ShippingAddress?.State,
                                        StreetNumber = ((fieldsUpdated?.Exists(pair => pair.Key == OrderDifference.DeliveryStreet)) ?? false) ? "" : order?.ShippingAddress?.StreetNumber,
                                        Title = order?.ShippingAddress?.Title,
                                        Custom = order?.ShippingAddress?.Custom
                                    }
                                }
                            );
                        }
                        break;

                    case OrderDifference.ExecutingFLoristDeliveryAmount:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetCustomFieldAction
                            {
                                Name = CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_DELIVERY_AMOUNT,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.ContactFirstName:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetShippingAddressCustomFieldAction()
                            {
                                Name = CtOrderCustomAttributesNames.ShippingAddress.CONTACT_FIRSTNAME,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.ContactLastName:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetShippingAddressCustomFieldAction()
                            {
                                Name = CtOrderCustomAttributesNames.ShippingAddress.CONTACT_LASTNAME,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.Message:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetCustomFieldAction
                            {
                                Name = CtOrderCustomAttributesNames.Order.MESSAGE,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.Signature:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetCustomFieldAction
                            {
                                Name = CtOrderCustomAttributesNames.Order.SIGNATURE,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.RecipientLongitude:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetShippingAddressCustomFieldAction
                            {
                                Name = CtOrderCustomAttributesNames.ShippingAddress.LONGITUDE,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.RecipientLatitude:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetShippingAddressCustomFieldAction
                            {
                                Name = CtOrderCustomAttributesNames.ShippingAddress.LATITUDE,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.RecipientTitle:
                        orderUpdate.Actions.Add
                       (
                           new OrderSetShippingAddressCustomFieldAction
                           {
                               Name = CtOrderCustomAttributesNames.ShippingAddress.CONTACT_TITLE,
                               Value = field.Value
                           }
                       );
                        break;
                    case OrderDifference.FloristInvoicePrinted:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetCustomFieldAction()
                            {
                                Name = CtOrderCustomAttributesNames.Order.FLORIST_INVOICE_PRINTED,
                                Value = field.Value
                            }
                        );
                        break;

                    case OrderDifference.FloristInvoiceUrl:
                        orderUpdate.Actions.Add
                        (
                            new OrderSetCustomFieldAction
                            {
                                Name = CtOrderCustomAttributesNames.Order.EXECUTING_FLORIST_INVOICE_URL,
                                Value = field.Value,
                            }
                        );
                        break;

                    case OrderDifference.InternalOrderId:
                        orderUpdate.Actions.Add
                        (
                              new OrderSetCustomFieldAction
                              {
                                  Name = CtOrderCustomAttributesNames.Order.INTERNAL_ORDER_ID,
                                  Value = field.Value,
                              }
                        );
                        break;

                    default:
                        break;
                }
            }
            return orderUpdate;
        }

        private async Task<IOrder> PostOrderUpdate(OrderUpdate orderUpdate, string orderId) => await _commerceToolsClient.WithApi().WithProjectKey(_projectKey).Orders()
            .WithId(orderId)
            .Post(orderUpdate)
            .ExecuteAsync();

        public async Task<IOrder> PostOrderUpdateWithRetry(OrderUpdate orderUpdate, string orderId, string orderNumber)
        {
            try
            {
                return await PostOrderUpdate(orderUpdate, orderId);
            }
            catch (System.Exception e)
            {
                _logger.LogInformation(e, "Failed to update order {orderNumber} => retry with GetOrder", orderNumber);

                IOrder order = await GetByOrderNumber(orderNumber);
                if (order == null)
                {
                    _logger.LogError("Order {orderNumber} not found for retry", orderNumber);
                }
                else
                {
                    // we use the last version number from CT order
                    orderUpdate.Version = order.Version;

                    try
                    {
                        return await PostOrderUpdate(orderUpdate, order.Id);
                    }
                    catch (System.Exception ex)
                    {
                        _logger.LogError(ex, "Failed to update order {orderNumber} after retry with GetOrder : Exception -> {ex}", orderNumber,ex.ToString());
                    }
                }
            }
            return null;
        }

       

        public async Task<List<KeyValuePair<OrderDifference, object>>> GetDifferencesForUpdate<T>(T? payload, IOrder ctOrder) where T : BaseOrderPayload
        {
            List<KeyValuePair<OrderDifference, object>> differences = new();
            if (payload == null)
                return differences;

            var fieldComparer = new FieldComparer();
            //Status
            var incomingCTStatus = GetCtStatusFromFrRAOStatus(payload.Status?.Status ?? string.Empty);
            if (!fieldComparer.Equals(incomingCTStatus, ctOrder.GetFloristOrderStatus()))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.Status, incomingCTStatus));

            // SalesOrigin
            if (!fieldComparer.Equals(payload.GetSalesOrigin().ToString(), ctOrder.GetOrderSalesOrigin()))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.SalesOrigin, payload.GetSalesOrigin().ToString() ?? string.Empty));

            // OrderOriginSystem
            if (!fieldComparer.Equals(payload.Source, ctOrder.GetOrderOriginSystem()))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.OriginSystem, payload.Source ?? string.Empty));

            // CreationDate
            if (payload.OrderDate == new DateTime(1900, 1, 1) || ctOrder.GetCreationDate() != payload.OrderDate)
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.CreationDate, payload.OrderDate.ToUniversalTime()));
            var timestamp = new DateTimeOffset(payload.OrderDate.ToUniversalTime()).ToUnixTimeMilliseconds();
            if (payload.OrderDate == new DateTime(1900, 1, 1) || ctOrder.GetCreationDateTimestamp() != timestamp)
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.CreationDateTimeStamp, timestamp));
            

            // ExecutingFlorist
            if (!fieldComparer.Equals(payload.FloristId, ctOrder.GetExecutingFloristId()))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.ExecutingFloristId, payload.FloristId ?? string.Empty));

            // TransmitterFlorist
            if (!string.IsNullOrWhiteSpace(payload.TransmitterFloristId) && int.TryParse(payload.TransmitterFloristId, out int result) && result > 0 &&
                !fieldComparer.Equals(payload.TransmitterFloristId, ctOrder.GetTransmitterFloristId()))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.TransmitterFloristId, payload.TransmitterFloristId ?? string.Empty));

            // DeliveryDate
            if (ctOrder.GetDeliveryDate()?.Date.ToString("yyyy-MM-dd") != payload.Delivery?.Date.ToString("yyyy-MM-dd"))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryDate, payload.Delivery?.Date.ToString("yyyy-MM-dd") ?? string.Empty));

            // DeliveryMoment
            MomentEnum targetWindow = payload.Delivery!.WindowRaoToMomentCT();
           

            string deliveryMoment = ctOrder.GetDeliveryMoment();

            // Special case for Evening since it doesn't exist, WILL prevent changing from Evening to Wholeday
            if (deliveryMoment == "3" && targetWindow == MomentEnum.Wholeday)
                deliveryMoment = ((int)MomentEnum.Wholeday).ToString();

            if (deliveryMoment != ((int)targetWindow).ToString())
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryMoment, ((int)targetWindow).ToString()));


            // DeliveryTime
            HandleDeliveryTimeDifference(differences, payload.Delivery?.Time ?? string.Empty, ctOrder.GetDeliveryTime() ?? string.Empty);

            // Add combined DeliveryMomentTime using values from differences list
            if (differences.Exists(d => d.Key == OrderDifference.DeliveryTime) ||
                differences.Exists(d => d.Key == OrderDifference.DeliveryMoment))
            {
                // Extract calculated values from differences list
                var momentDiff = differences.Find(d => d.Key == OrderDifference.DeliveryMoment);
                var timeDiff = differences.Find(d => d.Key == OrderDifference.DeliveryTime);

                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryMomentTime, new Dictionary<string, string>
                {
                    { "Moment", momentDiff.Value?.ToString() ?? "" },
                    { "Time", timeDiff.Value?.ToString() ?? "" }
                }));
            }

            // ExecutingFLoristDeliveryAmount
            if (payload!.Delivery!.TripCost.HasValue && payload!.Delivery!.TripCost.Value > 0)
            {
                if (!fieldComparer.Equals(payload!.Delivery!.TripCost, ctOrder.GetDeliveryExecutingAmount()))
                    differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.ExecutingFLoristDeliveryAmount, new Money { CentAmount = (long)(payload!.Delivery!.TripCost.Value * 100), CurrencyCode = "EUR" }));
            }

            // Recipient --> if the Greetings is empty in Legacy side we just skip the update into CT (no null value allowed)
            var recipientTitle = payload.Recipient?.Greetings?.GreetingsRaoToRecipientTitleCT() ?? "MRS";
            if (!string.IsNullOrWhiteSpace(recipientTitle))
            {
                if (!fieldComparer.Equals(recipientTitle, ctOrder.GeContactTitle()))
                    differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.RecipientTitle, recipientTitle!));
            }

            if (!fieldComparer.Equals(payload.Recipient?.FirstName, ctOrder.ShippingAddress.FirstName))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.RecipientFirstName, payload.Recipient?.FirstName ?? string.Empty));

            if (!fieldComparer.Equals(payload.Recipient?.LastName, ctOrder.ShippingAddress.LastName))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.RecipientLastName, payload.Recipient?.LastName ?? string.Empty));

            if (!fieldComparer.Equals(payload.Recipient?.MainPhone, ctOrder.ShippingAddress.Mobile))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.RecipientPhoneNumber, payload.Recipient?.MainPhone ?? string.Empty));

            //FullName
            if (differences.Any(d => d.Key == OrderDifference.RecipientFirstName) ||
               differences.Any(d => d.Key == OrderDifference.RecipientLastName))
            {
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.RecipientFullName, new Dictionary<string, string>
                {
                    { "RecipientFirstName", payload.Recipient?.FirstName ?? string.Empty },
                    { "RecipientLastName", payload.Recipient?.LastName ?? string.Empty }
                }));
            }


            // Message
            if (!fieldComparer.Equals(payload.Message, ctOrder.GetMessage()))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.Message, payload.Message ?? string.Empty));

            // Signature
            if (!fieldComparer.Equals(payload.Signature, ctOrder.GetSignature()))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.Signature, payload.Signature ?? string.Empty));

            // DeliveryCompanyName (lieu liv)
            if (!fieldComparer.Equals(payload.Delivery?.Place, ctOrder.ShippingAddress.Company))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryCompany, payload.Delivery?.Place ?? string.Empty));

            // DeliveryAdress liv
            if (!fieldComparer.Equals(payload.Recipient?.Street, ctOrder.ShippingAddress.StreetNumber + " " + ctOrder.ShippingAddress.StreetName))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryStreet, payload.Recipient?.Street ?? string.Empty));

            // DeliveryCity liv
            if (!fieldComparer.Equals(payload.Recipient?.City, ctOrder.ShippingAddress.City))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryCity, payload.Recipient?.City ?? string.Empty));

            // DeliveryZipCode
            if (!fieldComparer.Equals(payload.Recipient?.ZipCode, ctOrder.ShippingAddress.PostalCode))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryZipCode, payload.Recipient?.ZipCode ?? string.Empty));

            //DeliveryCountryCode
            if (!fieldComparer.Equals(payload.Recipient?.CountryCode, ctOrder.ShippingAddress.Country))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryCountryCode, payload.Recipient?.CountryCode ?? string.Empty));

            // DeliveryInfos
            if (!fieldComparer.Equals(payload.Delivery?.AdditionalAddress, ctOrder.ShippingAddress.AdditionalAddressInfo))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryAdditionalInfo, payload.Delivery?.AdditionalAddress ?? string.Empty));

            // DeliveryInstruction
            if (!fieldComparer.Equals(payload.Delivery?.Instructions, ctOrder.ShippingAddress.GetComments()))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryInstructions, payload.Delivery?.Instructions ?? string.Empty));

            //// Delivery type 
            //// Comment this for now as we dont want to change the delivery type after the order is created
            //string locationType = payload.Delivery?.LocationType switch
            //{
            //    "c" => "CEREMONY",
            //    "a" => "GRAVE",
            //    "d" => "DOMICILE", 
            //    _ => "GRAVE"
            //};
            //if ((ctOrder.GetDeliveryMode() ?? string.Empty) != (locationType ?? string.Empty))
            //    differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryMode, locationType ?? string.Empty));

            // Latitude
            object? ctLatitudeObject = ctOrder.ShippingAddress.Custom.Fields.FirstOrDefault(cf => cf.Key == CtOrderCustomAttributesNames.ShippingAddress.LATITUDE).Value; 
            try
            {
                double ctLatitude = Convert.ToDouble(ctLatitudeObject);
                if (ctLatitude != (payload.Recipient?.Latitude ?? 0))
                    differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.RecipientLatitude, payload.Recipient?.Latitude ?? 0));
            }
            catch(Exception ex)
            {
                _logger.LogError(ex, "Failed to get difference latitude on order {orderNumber} with Ct value : {ctLatitudeObject} and Rao value : {raoLatitude}, Exception : {ex}", ctOrder.OrderNumber, ctLatitudeObject.Serialize(), payload.Recipient?.Latitude.Serialize(), ex.ToString());
            }


            // Longitude
            object? ctLongitudeObject = ctOrder.ShippingAddress.Custom.Fields.FirstOrDefault(cf => cf.Key == CtOrderCustomAttributesNames.ShippingAddress.LONGITUDE).Value;
            try
            {
                double ctLongitude = Convert.ToDouble(ctLongitudeObject);
                if (ctLongitude != (payload.Recipient?.Longitude ?? 0))
                    differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.RecipientLongitude, payload.Recipient?.Longitude ?? 0));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get difference longitude on order {orderNumber} with Ct value : {ctLongitudeObject} and Rao value : {raoLongitude}, Exception : {ex}", ctOrder.OrderNumber, ctLongitudeObject.Serialize(), payload.Recipient?.Longitude.Serialize(), ex.ToString());
            }

            // Coordinates
            if (differences.Any(d => d.Key == OrderDifference.RecipientLatitude) ||
              differences.Any(d => d.Key == OrderDifference.RecipientLongitude))
            {
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.RecipientCoordinates, new Dictionary<string, double>
                {
                    { CtOrderCustomAttributesNames.ShippingAddress.LATITUDE, payload.Recipient?.Latitude ?? 0 },
                    { CtOrderCustomAttributesNames.ShippingAddress.LONGITUDE, payload.Recipient?.Longitude ?? 0 }
                }));
            }

            //Delivery Address
            if (differences.Any(d => d.Key == OrderDifference.DeliveryCompany) ||
               differences.Any(d => d.Key == OrderDifference.DeliveryStreet) ||
               differences.Any(d => d.Key == OrderDifference.DeliveryCity) ||
               differences.Any(d => d.Key == OrderDifference.DeliveryZipCode))
            {
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryAddress, new Dictionary<string, object>
                {
                    { "Company", payload.Delivery?.Place ?? string.Empty},
                    { "Street", payload.Recipient?.Street ?? string.Empty},
                    { "City", payload.Recipient?.City ?? string.Empty},
                    { "PostalCode", payload.Recipient?.ZipCode ?? string.Empty},
                    { "Coordinates",  new Dictionary<string, double>
                        {
                            { CtOrderCustomAttributesNames.ShippingAddress.LATITUDE, payload.Recipient?.Latitude ?? 0},
                            { CtOrderCustomAttributesNames.ShippingAddress.LONGITUDE, payload.Recipient?.Longitude ?? 0 }
                        }
                     }
                }));
            }

            // Contact
            if (!fieldComparer.Equals(payload.Delivery?.ContactFirstName, ctOrder.ShippingAddress.GetContactFirstName()))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.ContactFirstName, payload.Delivery?.ContactFirstName ?? string.Empty));

            if (!fieldComparer.Equals(payload.Delivery?.ContactLastName, ctOrder.ShippingAddress.GetContactLastName()))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.ContactLastName, payload.Delivery?.ContactLastName ?? string.Empty));

            //CustomerFirstName
            if (!fieldComparer.Equals(payload.Customer?.FirstName, ctOrder.BillingAddress.FirstName))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.CustomerFirstName, payload.Customer?.FirstName ?? string.Empty));

            //CustomerLastName
            if (!fieldComparer.Equals(payload.Customer?.LastName, ctOrder.BillingAddress.LastName))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.CustomerLastName, payload.Customer?.LastName ?? string.Empty));

            //CustomerEmail
            if (!fieldComparer.Equals(payload.Customer?.Email, ctOrder.BillingAddress.Email))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.CustomerEmail, payload.Customer?.Email ?? string.Empty));

            //CustomerPhoneNumber
            if (!fieldComparer.Equals(payload.Customer?.Phone, ctOrder.BillingAddress.Mobile))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.CustomerPhoneNumber, payload.Customer?.Phone ?? string.Empty));

            //CustomerCompany
            if (!fieldComparer.Equals(payload.Customer?.CompanyName, ctOrder.BillingAddress.Company))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.CustomerCompany, payload.Customer?.CompanyName ?? string.Empty));

            //BillingStreet
            if (!fieldComparer.Equals(payload.Billing?.Street, ctOrder.BillingAddress.StreetName))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.BillingStreet, payload.Billing.Street ?? string.Empty));

            //BillingCity
            if (!fieldComparer.Equals(payload.Billing?.City, ctOrder.BillingAddress.City))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.BillingCity, payload.Billing.City ?? string.Empty));

            //BillingZipCode
            if (!fieldComparer.Equals(payload.Billing?.ZipCode, ctOrder.BillingAddress.PostalCode))
                differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.BillingZipCode, payload.Billing.ZipCode ?? string.Empty));

            //Products
            try
            {
                // allow us to filter Foreign orders because we use CustomLineItems for non FR orders
                if (ctOrder.LineItems != null && ctOrder.LineItems.Count > 0) 
                {
                    var incomingRaoLineItems = payload.Products?.GetEligibleProducts().GroupByBundleProducts().Where(x => !x.IsDiscountLineItem()).ToList() ?? [];
                    if (incomingRaoLineItems.Count == 0)
                        throw new Exception(string.Format("GetEligibleProducts for order {0} returns no product by checking following RAO products : {1}", payload.OrderId, incomingRaoLineItems?.Serialize()));

                    //serialize 2 list log
                    _logger.LogInformation($"RaoLineItems compared : {incomingRaoLineItems.Serialize()}");
                    var CTLineItems = new List<ProductInformations>();
                    foreach (var lineItem in ctOrder.LineItems)
                    {
                        CTLineItems.Add(lineItem.MapLineItemToRAOLineItem());
                    }
                    _logger.LogInformation($"CTLineItems compared : {CTLineItems.Serialize()}");

                    if (IsAnyLineItemsDifference(incomingRaoLineItems, CTLineItems))
                    {
                        List<IOrderEditUpdateAction> lineItemsUpdateActions = [];
                        IChannel channel = await GetChannel(ctOrder.OrderNumber);
                        foreach (var ctLineItem in ctOrder.LineItems)
                        {
                            lineItemsUpdateActions.Add(new OrderEditAddStagedActionAction { StagedAction = new StagedOrderRemoveLineItemAction { LineItemId = ctLineItem.Id } });
                        }
                        foreach (var raoLineItem in incomingRaoLineItems)
                        {
                            StagedOrderAddLineItemAction? stagedOrderAddLineItemAction = await GetStagedOrderAddLineItemActionLocalOrder("EUR", ConvertRaoLineItemToGlobalOrderProduct(raoLineItem), channel.Id);
                            if (stagedOrderAddLineItemAction != null)
                                lineItemsUpdateActions.Add(new OrderEditAddStagedActionAction { StagedAction = stagedOrderAddLineItemAction });
                            else
                                throw new Exception(string.Format("Cannot create StagedOrderAddLineItemAction for product {0} for order {1} during full replace process", raoLineItem.ProductId));

                            differences.Add(new(OrderDifference.UpdateProduct, raoLineItem));
                        }

                        List<LineItemPrice> mongoLineItemsPrice = [];
                        var orderProducts = payload.Products?.GetEligibleProducts();
                        orderProducts?.FindAll(p => p.IsBundlePart() && !p.IsDiscountLineItem()).ForEach(p => mongoLineItemsPrice.Add(new LineItemPrice(p.GetProductKey(), p.GetVariantKey(), Convert.ToDecimal(p.Privex))));
                        if (mongoLineItemsPrice.Any())
                        {
                            var orderLineItemsPrices = new OrderLineItemsPrices(ctOrder.Id, mongoLineItemsPrice);
                            await (_orderLineItemsPricesRepository?.ReplaceOneAsync(orderLineItemsPrices) ?? Task.CompletedTask);
                            _logger.LogInformation($"OrderLineItemsPrices inserted : {orderLineItemsPrices.Serialize()}");

                        }

                        if (lineItemsUpdateActions.Count > 0)
                            differences.Add(new(OrderDifference.UpdateCTLineItems, lineItemsUpdateActions));



                        //List<IOrderUpdateAction> updateActions = [];
                        //if (incomingProducts.Where(product => string.IsNullOrEmpty(product.BundleId)).Count() != ctOrder.LineItems.Where(li => !li.IsBundle()).Count() // ensure same number of lineItems on both side considering bundled lineItems in CT
                        //    && Convert.ToInt64(incomingProducts.Where(product => !product.IsDiscountLineItem()).Count()) != ctOrder.LineItems.Sum(li => li.Quantity) // ensure same number of lineItems on both side considering lineItems not bundled in CT
                        //    && incomingProducts.GroupByBundleProducts().Where(product => !product.IsDiscountLineItem()).Count() != ctOrder.LineItems.Count) // ensure same number of lineItems on both side considering bundled lineItems in incomingProduct
                        //{
                        //    await SendSlackLog(string.Format("Order Products sync: Not same number of lineItems within the order {0}, but still try to update for incoming product {1}", payload.OrderId, string.Join(", ", incomingProducts.Select(p => p.ProductId))));
                        //}





                        //foreach (var lineItem in ctOrder.LineItems)
                        //{
                        //    bool isMatching = false;
                        //    foreach (ProductInformations incomingProduct in incomingRaoLineItems.GroupByBundleProducts())
                        //    {
                        //        if (lineItem.IsBundle())
                        //        {
                        //            if (incomingProduct.BundleId == lineItem.ProductKey)
                        //            {
                        //                HandleProductDifference(updateActions, incomingProduct, lineItem);
                        //                if (!IsEligibleAmount(incomingProduct => incomingProduct.BundleId!, incomingRaoLineItems, lineItem, lineItem => lineItem.GetBasePrice()))
                        //                    await SendSlackLog(string.Format("Order Products sync: total amount of lineItem differ between CT Bundle lineItem {0} and incoming RAO products {1} for order {2}, still keep update", lineItem.Variant.Sku, incomingRaoLineItems?.Serialize(), payload.OrderId));
                        //                isMatching = true;
                        //                continue;
                        //            }
                        //            else
                        //            {
                        //                var incomingProductKey = incomingProduct?.BundleId?.Split('-').FirstOrDefault() ?? incomingProduct.ProductId;
                        //                if (incomingProductKey == lineItem.ProductKey)
                        //                {
                        //                    if (IsEligibleAmount(incomingProduct => incomingProduct?.BundleId?.Split('-').FirstOrDefault() ?? incomingProduct.ProductId, incomingRaoLineItems, lineItem, lineItem => lineItem.GetBasePrice()))
                        //                        HandleProductDifference(updateActions, incomingProduct, lineItem);
                        //                    else
                        //                        await ReplaceLineItem(channel, lineItem, incomingProduct, lineItemsUpdateActions, payload.OrderId);
                        //                    isMatching = true;
                        //                    continue;
                        //                }
                        //            }
                        //        }
                        //        else
                        //        {
                        //            if (incomingProduct.GetProductKey() == lineItem.ProductKey)
                        //            {
                        //                HandleProductDifference(updateActions, incomingProduct, lineItem);

                        //                if (lineItem.IsUpdateNeeded(incomingProduct))
                        //                    differences.Add(new(OrderDifference.UpdateProduct, lineItem.MapLineItemToRAOLineItem()));

                        //                if (!IsEligibleAmount(incomingProduct => incomingProduct.GetProductKey(), incomingRaoLineItems.GroupByBundleProducts(), lineItem, lineItem => lineItem.GetPrice()))
                        //                    await SendSlackLog(string.Format("Order Products sync : Rao LineItem {0} has a different Price than his matching CommerceTools LineItem for order {1}, still keep update", incomingProduct.ProductId, payload.OrderId));
                        //                isMatching = true;
                        //                continue;
                        //            }
                        //            else if (incomingProduct.Price == Convert.ToDouble(lineItem.GetPrice()) && incomingRaoLineItems.Where(incomingProduct => incomingProduct.GetProductKey() == lineItem.ProductKey).Count() == 0)//case when lineItem has to be updated because we can retrieve it with the same price even it has a new productId, need to replace by delete then add
                        //            {
                        //                await ReplaceLineItem(channel, lineItem, incomingProduct, lineItemsUpdateActions, payload.OrderId);
                        //                isMatching = true;
                        //                continue;
                        //            }
                        //        }

                        //    }
                        //    if (!isMatching)
                        //        lineItemsUpdateActions.Add(new OrderEditAddStagedActionAction { StagedAction = new StagedOrderRemoveLineItemAction { LineItemId = lineItem.Id } });
                        //}
                        //if (updateActions.Count > 0)
                        //    differences.Add(new(OrderDifference.UpdateCTFieldsProductList, updateActions));


                    }
                }

            }
            catch(Exception ex)
            {
                await SendSlackLog(string.Format("Order Products sync: skip lineItems update for the order {0}, Exception : {1}", ctOrder.OrderNumber, ex.ToString()));
            }
            return differences;
        }
        private bool IsAnyLineItemsDifference(List<ProductInformations> incomingLineItems, List<ProductInformations> oldLineItems)
        {

            // Count comparison
            if (incomingLineItems.Count != oldLineItems.Count)
            {
                _logger.LogInformation("Mismatch in LineItems count — triggering full replace");
                return true;
            }

            // ProductId comparison
            if (!incomingLineItems
                .OrderBy(x => x.ProductId)
                .Select(x => x.ProductId)
                .SequenceEqual(oldLineItems
                    .OrderBy(x => x.ProductId)
                    .Select(x => x.ProductId)))
            {
                _logger.LogInformation("Mismatch in LineItems ProductId — triggering full replace");
                return true;
            }

            // VariantKey comparison
            if (!incomingLineItems
                .OrderBy(x => x.GetVariantKey())
                .Select(x => x.GetVariantKey())
                .SequenceEqual(oldLineItems
                    .OrderBy(x => x.GetVariantKey())
                    .Select(x => x.GetVariantKey())))
            {
                _logger.LogInformation("Mismatch in LineItems VariantKey — triggering full replace");
                return true;
            }

            // Price comparison
            if (!incomingLineItems
                .OrderBy(x => x.Price)
                .Select(x => x.Price)
                .SequenceEqual(oldLineItems
                    .OrderBy(x => x.Price)
                    .Select(x => x.Price)))
            {
                _logger.LogInformation("Mismatch in LineItems Price — triggering full replace");
                return true;
            }


            return false;
        }
        //private bool IsEligibleAmount(Func<ProductInformations, string> raoLineItemKeyComparison, List<ProductInformations> incomingProducts, ILineItem lineItemCompared, Func<ILineItem, decimal> priceComparison)
        //{
        //    var raoLineItemTotalAmount = Math.Round(incomingProducts
        //        .Where(raoLineItem => raoLineItemKeyComparison(raoLineItem) == lineItemCompared.ProductKey)
        //        .Sum(raoLineItem => raoLineItem.Price * raoLineItem.Quantity), 2);

        //    return raoLineItemTotalAmount == Convert.ToDouble(priceComparison(lineItemCompared));    
        //}
        //private async Task ReplaceLineItem(IChannel channel, ILineItem lineItemToReplace, ProductInformations incomingProduct, List<IOrderEditUpdateAction> lineItemsUpdateActions, string orderNumber)
        //{
        //    channel = await GetChannel(channel, async (ex) => await SendSlackLog(string.Format("Order Products sync: Cannot get channel with key {0} for replace lineItem {1} to {2} for order {3}, Exception {4}", _commonSettings.CurrentValue.LocalCountryChannelKey, lineItemToReplace.Key, incomingProduct.ProductId, orderNumber, ex.ToString())));
        //    if (channel != null)
        //    {
        //        StagedOrderAddLineItemAction? stagedOrderAddLineItemAction = await GetStagedOrderAddLineItemActionLocalOrder("EUR", ConvertRaoLineItemToGlobalOrderProduct(incomingProduct), channel.Id);
        //        if (stagedOrderAddLineItemAction != null)
        //        {
        //            lineItemsUpdateActions.Add(new OrderEditAddStagedActionAction { StagedAction = new StagedOrderRemoveLineItemAction { LineItemId = lineItemToReplace.Id } });
        //            lineItemsUpdateActions.Add(new OrderEditAddStagedActionAction { StagedAction = stagedOrderAddLineItemAction });
        //        }
        //        else
        //            await SendSlackLog(string.Format("Order Products sync: Cannot create StagedOrderAddLineItemAction for product {0} for order {1}, skip update", incomingProduct.ProductId));
                
        //    }
        //}


        private async Task<IChannel> GetChannel(string orderNumber)
        {
            try
            {
                var channel = await _channelService.GetByKey(_commonSettings.CurrentValue.LocalCountryChannelKey);
                if (channel is null)
                    throw new Exception("CT returns null channel");
                return channel;
            }
            catch (Exception ex)
            {
                throw new Exception(string.Format("Cannot get channel with key {0} in order to perform full replace lineItem for order {1}, Exception {2}", _commonSettings.CurrentValue.LocalCountryChannelKey,  orderNumber, ex.ToString()));
            }
        }


        private async Task SendSlackLog(string log)
        {
            _logger.LogError(log);
            await(_slackAlertService?.SendErrorAlertAsync(log) ?? Task.CompletedTask);
        }

        private void HandleProductDifference(List<IOrderUpdateAction> updateActions, ProductInformations raoLineItem, ILineItem ctLineItem)
        {

            
            if (raoLineItem.Privex != Convert.ToDouble(ctLineItem.GetExecutingFloristAmount()))
            {
                updateActions.Add(new OrderSetLineItemCustomFieldAction
                {
                    LineItemId = ctLineItem.Id,
                    Name = CtOrderCustomAttributesNames.LineItem.EXECUTING_FLORIST_AMOUNT,
                    Value = new CentPrecisionMoney { CentAmount = (long)(raoLineItem.Privex * 100), CurrencyCode = "EUR", FractionDigits = 2 }
                });

            }
            if (raoLineItem.Margin != Convert.ToDouble(ctLineItem.GetMarketingFee()))
            {
                updateActions.Add(new OrderSetLineItemCustomFieldAction
                {
                    LineItemId = ctLineItem.Id,
                    Name = CtOrderCustomAttributesNames.LineItem.MARKETING_FEE,
                    Value = new CentPrecisionMoney { CentAmount = (long)(raoLineItem.Margin * 100), CurrencyCode = "EUR", FractionDigits = 2 }
                });

            }
            
            

            if (!(raoLineItem.RibbonText ?? string.Empty).Equals(ctLineItem.GetRibbonText() ?? string.Empty, StringComparison.OrdinalIgnoreCase))
            {
                updateActions.Add(new OrderSetLineItemCustomFieldAction
                {
                    LineItemId = ctLineItem.Id,
                    Name = CtOrderCustomAttributesNames.LineItem.RIBBON_TEXT,
                    Value = raoLineItem.RibbonText
                });
            }

            if (!(raoLineItem.Description?.Trim() ?? string.Empty).Equals(ctLineItem.GetComposition()?.Trim() ?? string.Empty, StringComparison.OrdinalIgnoreCase))
            {
                updateActions.Add(new OrderSetLineItemCustomFieldAction
                {
                    LineItemId = ctLineItem.Id,
                    Name = CtOrderCustomAttributesNames.LineItem.COMPOSITION,
                    Value = raoLineItem.Description
                });
            }


        }

        public void HandleDeliveryTimeDifference(List<KeyValuePair<OrderDifference, object>> differences, string deliveryTimeReceived, string deliveryTimeToCheck)
        {
            if (TimeSpan.TryParse(deliveryTimeReceived, out var timeReceived))
            {
                if (TimeSpan.TryParse(deliveryTimeToCheck, out var timeToCheck))
                {
                    // Compare only hours and minutes
                    if (timeReceived.Hours != timeToCheck.Hours || timeReceived.Minutes != timeToCheck.Minutes)
                    {
                        differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryTime, timeReceived.ToString(@"hh\:mm")));
                    }
                }
                else
                    differences.Add(new KeyValuePair<OrderDifference, object>(OrderDifference.DeliveryTime, timeReceived.ToString(@"hh\:mm")));
            }
        }

        public async Task<Result> UpdateOrderFromRAOToCT(IOrder ctOrder, List<KeyValuePair<OrderDifference, object>> fieldsUpdated) =>
            await Result.Try(async () =>
            {
                var orderupdate = CreateOrderUpdate(ctOrder, fieldsUpdated);
                _logger.LogInformation("Updating order {orderNumber} with {fieldsUpdated} in UpdateOrderFromRAOToCT process", ctOrder.OrderNumber, fieldsUpdated?.Serialize());
                await PostOrderUpdateWithRetry(orderupdate, ctOrder.Id, ctOrder.OrderNumber);
            },ex => ex?.ToString());




        #endregion

        public async Task<T> BuildOrderMessageFromRaoOrderMessage<T, T1, T2, T3>(T2 RAOMessage, string ctOrderId = "")
           where T : BaseMessage<T1>, new()
           where T1 : IPayload
           where T2 : BaseMessage<T3>
           where T3 : BaseOrderPayload
        {

            var convertedMessage = new T
            {
                CreatedAt = DateTime.Now,
                MessageId = Guid.NewGuid().ToString(),
                NbTry = 0,
                CausationId = RAOMessage?.CausationId,
                DistributedTracingData = RAOMessage?.DistributedTracingData,
            };


            convertedMessage.Payload = convertedMessage switch
            {
                LegacyOrderCreatedMessage message => (T1)(object)message.ConvertPayload(RAOMessage?.Payload!),
                LegacyOrderAssignedMessage message => (T1)(object)message.ConvertPayload(RAOMessage?.Payload!, ctOrderId),
                OrderUpdatedMessage message => (T1)(object)new OrderUpdatedPayload(RAOMessage?.Payload!),
                OrderAssignmentMessage message => (T1)(object)new OrderAssignmentPayload(RAOMessage?.Payload!),
                _ => default
            };

            return convertedMessage;

        }
    }

    public class GetLineItemForLocalOrder_Result
    {
        public IProduct Product { get; set; }
        public LineItemDraft LineItem { get; set; }
    }

    public enum OrderDifference
    {
        Status,
        SalesOrigin,
        OriginSystem,
        CreationDate,
        CreationDateTimeStamp,
        DeliveryDate,
        DeliveryMoment,
        DeliveryTime,
        DeliveryMomentTime,
        RecipientFirstName,
        RecipientLastName,
        RecipientTitle,
        RecipientPhoneNumber,
        RecipientFullName,
        ContactFirstName,
        ContactLastName,
        Message,
        Signature,
        DeliveryCompany,
        DeliveryStreet,
        DeliveryCity,
        DeliveryZipCode,
        DeliveryCountryCode,
        DeliveryAdditionalInfo,
        DeliveryInstructions,
        DeliveryMode,
        ExecutingFLoristDeliveryAmount,
        RecipientLatitude,
        RecipientLongitude,
        RecipientCoordinates,
        NewProduct,
        DeleteProduct,
        UpdateProduct,
        UpdateCTFieldsProductList,
        UpdateCTLineItems,
        DeliveryAddress,
        ExecutingFloristId,
        TransmitterFloristId,
        CustomerFirstName,
        CustomerLastName,
        CustomerEmail,
        BillingStreet,
        BillingCity,
        BillingZipCode,
        CustomerPhoneNumber,
        CustomerCompany,
        FloristInvoicePrinted,
        FloristInvoiceUrl,
        InternalOrderId
    }
}
