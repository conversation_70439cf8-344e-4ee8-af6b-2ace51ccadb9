﻿using ITF.Lib.Common.DomainDrivenDesign;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ITF.SharedModels.DataModels.Order
{
    public class OrderCustomerFeedback : BaseClass<string>
    {
        public string BusinessUnit { get; set; } = string.Empty;
        public DateTime? DeliveryDate { get; set; }
        public DateTime? OrderDate { get; set; }
        public DateTime? ResponseDate { get; set; }
        public string OrderId { get; set; } = string.Empty;
        public string FloristId { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
        public string ReasonCode { get; set; } = string.Empty;
        public string Score { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;

        public override void SetId() => Id = Guid.NewGuid().ToString();
    }
}
