﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Description>
		Interflora Library
		v 2.106.0 : Update libs
		v 2.105.0 : Update libs
		v 2.104.0 : Add customer verification email in customerService
		v 2.103.4 : Fix PSM in product comparison
		v 2.103.3 : Add FirstName and LastName in OrderDeliveryDTO
		v 2.103.2 : Add FirstName and LastName in OrderSummaryDTO
		v 2.103.1 : Timestamp cast long instead of int
		v 2.103.0 : Handle Timestamp creationDate
		v 2.102.0 : Rework latitude/longitude getDiff
		v 2.101.0 : Fix little cases for variant check in OrderReactor usage
		v 2.100.0 : Handle FR GFS Order OUT from IRIS for PostOrder
		v 2.99.0 : Update SharedLibs
		v 2.98.0 : Add DeleteCustomer method to CustomerService
		v 2.97.0 : Update sharedLib
		v 2.96.1 : Fix CT customer Query expand issue (others api)
		v 2.96.0 : Fix CT customer Query expand issue
		v 2.95.6 : Handle difference for ExecutingFLoristDeliveryAmount
		v 2.95.5 : Update billing Mobile instead of Phone Ct field
		v 2.95.4 : Fix Convert.ToInt for non numeric for the size
		v 2.95.3 : Added Stem cases for detecting product changes / fix variant key for getDiff
		v 2.95.2 : Fix REAdd DeleteOrderEdits method to OrderService
		v 2.95.1 : Fix product Sync ignore Acc Type for comparison
		v 2.95.0 : Add DeleteOrderEdits method to OrderService
		v 2.94.4 : Add vatNumber, companyNumber and Pec to globalOrder
		v 2.94.3 : Handle custom Field FloristInvoiceUrl and InternalOrderId in getdifference for update
		v 2.94.2 : Handle custom Field FloristInvoicePrinted get and update
		v 2.94.1 : Handle custom Field FloristInvoicePrinted
		v 2.94.0 : Update references
		v 2.93.1 : Fix product sync ignore foreign order update
		v 2.93.0 : Rework product sync for full erase
		v 2.92.1 : Rework expired due date for external price
		v 2.92.0 : Update reference ITF.SharedLibraries for ITF.SharedModels
		v 2.91.1 : Fix mapping executingFloristAmount with fee for productSync + logs
		v 2.91.0 : Add VAT informations in ProductDTO
		v 2.90.1 : Handle OrderCreationDate OrderSync
		v 2.90.0 : Handle SalesOrigin and EveningMoment OrderSync
		v 2.89.0 : Update SharedLibs to have the latest CT lib SDK version to use new CustomerGroupsAssignemtns features / Add UpdateCustomer Method in CustomerService
		v 2.88.4 : Fix Convert RaoProduct convert to LineItem without bundleId info
		v 2.88.3 : Fix getProduct by wrong bundle key in productSync
		v 2.88.2 : Rework single LineItem amount check in productSync
		v 2.88.1 : Remove LineItem amount check in productSync
		v 2.88.0 : Rework Sync Product
		v 2.87.1 : Patch references
		v 2.87.0 : Added executingFloristInvoiceUrl to the IT.SharedLibraries.CT.Orders.IOrderService.HandleOrderAssigned
		v 2.86.0 : handle extra_bundle_infos into order.lineitem (sweden)
		v 2.85.0 : Rework Bundle Extension Method
		v 2.84.0 : Handle Update of the composition field in CT for PSM FR
		v 2.83.6 : Rework mongo Line Item in update part
		v 2.83.5 : Rework total amount lineItem bundle comparison
		v 2.83.4 : Rework bundled CT lineItem Count comparison
		v 2.83.3 : Handle mongo LineItem only in creation part
		v 2.83.2 : Avoid no Bundle Discount LineItem in total lineItem Number Check
		v 2.83.1 : Avoid Discount LineItem creation
		v 2.83.0 : Handle bundle creation in CT
		v 2.82.0 : Add GetByOrderIdAsync for OrderLineItemsPricesRepository and fix some logic to replace instead of insert new line item prices
		v 2.81.0 : fix get diff for recipientTitle with null/empty handling to MRS
		v 2.80.3 : fix ?? in delivery adress comparison
		v 2.80.2 : fix STEM_SIZE constant
		v 2.80.1 : Handle 409 for update CTFields process sync order
		v 2.80.0 : Rework the OrderDifference.DeliveryStreet to handle the StreetNumber duplicate between CT and RAO
		v 2.79.0 : Add DeliveryDistance to OrderSummaryDto and OrderDto for FR needs
		v 2.78.5 : make IUnitOrderService in OrderService as nullable
		v 2.78.4 : product sync consider rem for no bundle case
		v 2.78.3 : product sync consider quantity for amount Check
		v 2.78.2 : order syn getDifferences use fieldComparer
		v 2.78.1 : handle quantity check in matching line item amount check
		v 2.78.0 : Add latitude and longitude for OrderSummaryDto too
		v 2.77.4 : fix typo version
		v 2.77.3 : remove last lib tag
		v 2.77.2 : test last lib tag
		v 2.77.1 : Fix cast to double : Add latitude and longitude extension method for Ct order
		v 2.77.0 : Add latitude and longitude extension method for Ct order
		v 2.76.4 : ProductSync rework update check
		v 2.76.3 : Add logs
		v 2.76.2 : Add constant to get florist preparation time attribute
		v 2.76.1 : Consider FloristFee for register mongoLineItemPrice
		v 2.76.0 : Addded new method to LineItemExtensions
		v 2.75.0 : Addded new method to AddressExtenions
		v 2.74.0 : Product sync consider quantity when compare line Items
		v 2.73.1 : non required ISlackAlertService injection in OrderService constructor
		v 2.73.0 : update lib
		v 2.72.0 : rework Greetings check difference
		v 2.71.0 : Add some logs and error managment for UpdateOrder into CT helping to debug the issue with the order update
		v 2.70.2 : ProductSync fix Recipient Name greetings comparison
		v 2.70.1 : ProductSync handle debundled product on both Rao/CT side
		v 2.70.0 : ProductSync totalAmount check fix
		v 2.69.0 : Reork Moment/window management in OrderService
		v 2.68.0 : Added method to AddressExtensionMethods
		v 2.67.3 : sync bundle inihb pending test
		v 2.67.2 : fix if Greeting is empty in Legacy side we just skip the update into CT (no null value allowed)
		v 2.67.1 : fix number of line item comparison in product difference
		v 2.67.0 : fix nullable orderLineItemsPricesRepository
		v 2.66.0 : adapt lib
		v 2.65.0 : sync bundle inihb pending test
		v 2.64.0 : Fix HandleDeliveryTimeDifference edges cases
		v 2.63.0 : Product Sync iteration 2
		v 2.62.0 : Update ITF.SharedLibraries ref to 8.25.0
		v 2.61.0 : adding context causationId from casted message for helping apm debugging
		v 2.60.0 : Fix HandleOrderAssigned defaultStatus not used
		v 2.59.0 : Fix HandleOrderAssigned variable always null
		v 2.58.0 : Fix publish messages without getting the previous MessageId from the initial message
		v 2.57.0 : First product syn iteration
		v 2.56.2 : Fix Test
		v 2.56.1 : Fix Test
		v 2.56.0 : Add extensions method for product sync
		v 2.55.0 : Rework the IOrderDtoService
		v 2.54.0 : Rework MarketingFee for CT
		v 2.53.1 : Re add default status value to HandleOrderExternalDeliveryFieldsUpdated
		v 2.53.0 : Add executingFloristInvoiceUrl Getter from CT + modifying the OrderDTO to add the new field
		v 2.52.0 : Add executingFloristInvoiceUrl in const for CT Order custom fields
		v 2.51.0 : Added field ExternalDeliveryTrackingCode to OrderSummaryDTO
		v 2.50.0 : Add SetExecutingFloristAmount method to OrderService
		v 2.49.1 : Update ITF.SharedLibraries reference to 8.19.2
		v 2.49.0 : Added extensions methods
		v 2.49.1 : Update ITF.SharedLibraries reference to 8.19.2
		v 2.49.0 : Added extensions methods
		v 2.48.8 : Remove default status value to HandleOrderExternalDeliveryFieldsUpdated
		v 2.48.7 : Update ITF.SharedLibraries reference to 8.17.0
		v 2.48.6 : Change status mapping for AFF to ACCEPTED
		v 2.48.5 : Update ITF.SharedLibraries reference to 8.16.0
		v 2.48.4 : Add default status value to HandleOrderExternalDeliveryFieldsUpdated
		v 2.48.3 : Add limit on delivery date for get to execute orders query
		v 2.48.2 : Force free price product for France when no match price found in CT
		v 2.48.1 : Handle Assign free price product for France
		v 2.48.0 : Fix Default Variant For free price with location
		v 2.47.0 : Handle ProductVariant expired Price with Roses a la tige case
		v 2.46.0 : Handle ProductVariant expired Price
		v 2.45.0 : Added methods to VariantExtensionMethods
		v 2.44.3 : Add HandleDeliveryTimes test method
		v 2.44.2 : Add GetToExecuteOrders test method
		v 2.44.1 : Add BuilderMessageConverter
		v 2.44.0 : Add GetToExecuteOrders test method
		v 2.43.2 : Fixed Evening getting removed from CT
		v 2.43.1 : Fixed the externalDeliveryTrackingCode parameter check in the UpdateOrderExternalDeliveryFields method
		v 2.43.0 : Added ExternalDeliveryTrackingCode to ExternalDeliveryDTO + extension method
		v 2.42.0 : Updated OrderService.HandleOrderExternalDeliveryFieldsUpdated
		v 2.41.0 : Remove delivery type from difference for order update
		v 2.40.0 : Added PaymentExtensionMethods
		v 2.39.1 : Add sort by internal order ID for GetToExecuteOrders - code fix
		v 2.39.0 : Add sort by internal order ID for GetToExecuteOrders
		v 2.38.0 : Update ITF.SharedModels ref to 8.9.0
		v 2.37.0 : Refacto GetDifferenceForOrderUpdate
		v 2.36.1 : GSEMIG-214 - Patch
		v 2.36.0 : GSEMIG-214 - Update ITF.SharedLibraries ref to 8.13.1
		v 2.35.0 : Added methods to VariantExtensions
		v 2.34.0 : FLR-1686 Update FloristStatus Ct to DELIVERED when UpdateStatusMessage bring DELIVERED deliveryStatus
		v 2.33.0 : Fix wrong fields added in PostOrderCT reacted to OrderPlaced/UpdatedMessage
		v 2.32.0 : Update Libs
		v 2.31.0 : Added methods to OrderExtensionMethods
		v 2.30.0 : Add a method to search duplicated orders
		v 2.29.0 : Update ITF.ShareLibraries reference to 8.11.0
		v 2.28.0 : Added methods to CustomLineItemExtensionMethods
		v 2.27.0 : Update RAO fields in CT
		v 2.26.1 : Update ITF.SharedModels reference to 8.4.1
		v 2.26.0 : Inhib Product Update CT through Messages
		v 2.25.0 : Added methods to AddressExtensionMethods
		v 2.24.1 : Patch VariantExtensions.GetBundleVariants
		v 2.24.0 : Added IOrderService.HandleOrderItemExecutorAmountUpdated
		v 2.23.0 : Update ITF.SharedModels reference to 8.3.0
		v 2.22.0 : Update GetDifferenceForUpdate Methode to handle PFS mapping update message trigger
		v 2.21.0 : Update Libs
		v 2.20.0 : Update Libs
		v 2.19.0 : Update ITF.SharedLibraries reference to 8.8.0
		v 2.18.0 : Added methods to VariantExtensions - GetProductClassification
		v 2.17.0 : Update ITF.SharedLibraries reference to 8.7.0
		v 2.16.0 : Added methods to VariantExtensions - GetProductType
		v 2.15.0 : Added methods to VariantExtensions - GetBundleVariantsObjectId
		v 2.14.0 : Replace delivery country code UK with GB into OrderService.HandleOrderCreated
		v 2.13.0 : Added methods to VariantExtensions
		v 2.12.1 : Added PaymentService - fix
		v 2.12.0 : Added PaymentService
		v 2.11.0 : Removed methods into VariantExtensions for accessing custom attribute available-colors
		v 2.10.0 : Replace methods into VariantExtensions to use attr(Label) instead of attr(VariantName)
		v 2.9.1  : Fix method to Update external delivery Fields for order CT
		v 2.9.0  : Added methods to Update external delivery Fields for order CT
		v 2.8.0  : Added methods to ProductService
		v 2.7.0  : Update ITF.SharedLibraries ref to 8.1.0
		v 2.6.0  : Added property BundleKey to IT.SharedLibraries.CT.ExtensionMethods.Domain.BundleVariant
		v 2.5.0  : Added methods to ProductExtensions
		v 2.4.0  : FLR-1581
		v 2.3.0  : Added methods to VariantExtensions
		v 2.2.0  : Added GetCartDiscountById to DiscountCodeService
		v 2.1.0  : Updated IT.SharedLibraries.CT.Orders.Services.IOrderDtoBuilderService and IOrderSummaryDtoBuilderService
		v 2.0.3  : FLR-1560 patch 2
		v 2.0.2  : Update reference ITF.SharedModels to 8.0.2
		v 2.0.1  : update libs
		v 2.0.0  : Update to .NET 8
		v 1.94.0 : DIGI-762 Added the common discount code processes
		v 1.93.3 : FLR-1560 patch
		v 1.93.2 : Enriched CustomObjectService + VariantExtensions + ProductService
		v 1.93.1 : Enriched CustomObjectService + VariantExtensions
		v 1.93.0 : Added CustomObjectService
		v 1.92.0 : Added methods to VariantExtensions
		v 1.91.1 : Patched GlobalOrderItemUpdated event management
		v 1.91.0 : Handle orderCreatedMessage that not contain invoiceFirstName/LastName
		v 1.90.1 : Re-throw Concurrent Exception (409) on order update
		v 1.90.0 : Added methods to LineItemExtensions
		v 1.89.0 : FLR-1560 [Spain and PT] Add 9€ of Marketing fees for GFS out order
		v 1.88.0 : Added methods to VariantExtensions
		v 1.87.0 : Added methods to VariantExtensions
		v 1.86.2 : fix typo
		v 1.86.1 : adapt address field to remove additionnalStreetInfo concatenation
		v 1.86.0 : add additionnalStreetInfo and companyName fields in delivertDto for order
		v 1.85.1 : fix typo
		v 1.85.0 : handle companyName fields shipping globalOrderModel
		v 1.84.0 : handle additionnalStreetInfo, invoiceFirstName, invoiceLastName and companyName fields shipping and billing globalOrderModel
		v 1.83.0 : Added new extension methods
		v 1.82.0 : Added methods to VariantExtensions
		v 1.81.0 : Added methods to VariantExtensions
		v 1.80.2 : Return zero with GetDeliveryPrice extension method in case of null value. External delivery informations rename
		v 1.80.1 : Patch VariantExtensions.SetSummary
		v 1.80.0 : Added methods to VariantExtensions
		v 1.79.0 : Added methods to VariantExtensions
		v 1.78.0 : Added methods to VariantExtensions
		v 1.77.0 : Added methods to Product and Category services
		v 1.76.0 : Added CT Variants extensions methods
		v 1.75.0 : Add Order reactor RAO FR messages handling for status update for CT Order
		v 1.74.0 : Added TaxCategoryService + extensions methods
		v 1.73.0 : Added new methods to IT.SharedLibraries.CT.ExtensionMethods.VariantExtensionMethods
		v 1.72.1 : add more delivery fields in orderSummaryDto
		v 1.72.0 : add delivery fields in orderSummaryDto
		v 1.71.0 : FLR-1497 Handle street Number, AdditionalStreetInfo and Province in billing address
		v 1.70.0 : Update references ITF.SharedLibraries to 7.44.2 and ITF.SharedModels to 7.67.0
		v 1.69.0 : Add contact first name and last name in order delivery DTO
		v 1.68.0 : Added new Ct extensions methods
		v 1.67.0 : Added IT.SharedLibraries.CT.ExtensionMethods.AddressExtensionMethods.GetContactTitle
		v 1.66.0 : update deps -&gt; so new CT SDK Version / adapt some little changes because of CT Update / Begin to add RAO FR Messages handling to update CT (FR MIG)
      v 1.65.0 : Add Messages and models for deliveryStatus and deliveryCost
      v 1.64.0 : Use shared Lib 7.33.0
      v 1.63.0 : FLR-1411 Handle florist_product_composition per country
      v 1.62.0 : FLR-1375 Handle OrderRecipientCoordinatesUpdated
      v 1.61.0 : Handle line Item composition for specific product
      v 1.60.1 : Move Region field in DeliveryDto
      v 1.60.0 : Add Region field in orderDto
      v 1.59.1 : Patch on IT.SharedLibraries.CT.Orders.OrderService.HandleOrderAssigned
      v 1.59.0 : FLR-847 Added IT.SharedLibraries.CT.Orders.Services.GetNationalShippingMethodKey
      v 1.58.0 : FLR-847 Update reference ITF.SharedModels to 7.59.1
      v 1.57.0 : Update reference
      v 1.56.0 : Added IOrderQueryService.GetOrderById
      v 1.55.0 : Added class AddressExtensionMethods
      v 1.54.0 : Fix confusion between Shipping Description and Billing Description
      v 1.53.0 : Added methods to IT.SharedLibraries.CT.ExtensionMethods.CustomLineItemExtensionMethods
      v 1.52.0 : FLR-1279: Hide unnecessary products from AllProduct MS + FLR-1277 Reset the Delivery in progress field when an order is assigned
      v 1.51.2 : minor fixes
      v 1.51.1 : handle more behavior about LimitAcceptedBefore Date
      v 1.51.0 : Fix get LimitAcceptedBefore Date
      v 1.50.0 : FLR-1250 Use the product amount for the executor when returning an order to PFS
      v 1.49.0 : FLR-1249 Add a new field into the Assignation Order Events to get the product amount for the executor
      v 1.48.0 : Handle the lack of custom fields in getDeliveryDate method
      v 1.47.0 : FLR-1427 Handle 9.99€ and 10.99€ delivery cost for Spain
      v 1.46.0 : Add itemNumber on orderDto
      v 1.45.0 : Add isModified bool on order in CT
      v 1.44.2 : Fix on UpdateFloristOrderStatus: remove the value of the executor florist when removal_assignment is called
      v 1.44.1 : Improved error log in IT.SharedLibraries.CT.Orders.OrderService.GetById
      v 1.44.0 : Updated OrderDTO, added ExecutingFlorist and TransmitterFlorist
      v 1.43.1 : Patch on HandleOrderCreated: set empty card message when it is null
      v 1.43.0 : FLR-1173 Added method IT.SharedLibraries.CT.ShippingMethods.IShippingMethodService.UpdateSetFixedShippingRate
      v 1.42.0 : Added method IT.SharedLibraries.CT.ShippingMethods.GetInternationalDeliveryFeeForPfs()
      v 1.41.0 : Delivery fee for international outbound order read from CT shipping method
      v 1.40.1 : Merge constant files
      v 1.40.0 : Add GetCustomString method to retrieve custom fields value
      v 1.39.0 : Add GetProductWithCategoriesById to get unpublished products (get IProduct not IProductProjection)
      v 1.38.0 : Update reference
      v 1.37.1 : FLR-1141 Order create event: handle equal products sent by legacy
      v 1.37.0 : Update reference
      v 1.36.3 : Fix assignation removal
      v 1.36.2 : Fix version
      v 1.36.1 : Fix references
      v 1.36.0 : Handle GlobalOrderModel.ExecutingFloristType
      v 1.35.2 : Added variants extensions methods
      v 1.35.1 : Fix method for OrderItemUpdated
      v 1.35.0 : Added variants extensions methods
      v 1.34.0 : FLR-1127 Handle accessory products for non Flower and Gifts executor use case
      v 1.33.1 : Fix version
      v 1.33.0 : Added IOrderDtoBuilderService and IOrderSummaryDtoBuilderService + ct extension methods
      v 1.32.1 : Fix use of shipping.comments instead of notes in global order model field
      v 1.32.0 : Add limit parameter to GetToExecuteOrders query
      v 1.31.0 : Added IT.SharedLibraries.CT.IShippingMethodService.GetDeliveryFeeForPfs
      v 1.31.0 : Added IT.SharedLibraries.CT.IShippingMethodService.GetDeliveryFeeForPfs
      v 1.30.0 : Use pfs specific shipping method
      v 1.29.1 : Updated method signatures for OrderService.GetCustomLineItemForForeignOrder and OrderService.GetLineItemForLocalOrder + added CtCustomException
      v 1.29.0 : Order Create: when the product allow price different from variants, mkfee are taken from the request, otherwise the value stored into the CT product is used
      v 1.28.7 : Added log info in case ExternalPrice for line item is setted
      v 1.28.6 : Patch on HandleOrderCreated on the evaluation of CT product variant
      v 1.28.5 : Patch on fetching line item - v2
      v 1.28.4 : Patch on fetching line item
      v 1.28.3 : Patch on marketingfee for national order
      v 1.28.2 : Handle marketingfee for national order + log shipping method
      v 1.28.1 : Use extentions methods for custom fields
      v 1.28.0 : Use more fields on OrderService
      v 1.27.2 : Patch on HandlerOrderCreated
      v 1.27.1 : Fix reference
      v 1.27.0 : handle order recipient name and phone number update
      v 1.26.1 : Fix DeliveryCountrycode
      v 1.26.0 : Set "PFS" as default source of order if source is empty. Update shared model version.
      v 1.25.0 : Update reference
      v 1.24.0 : added new constant "composition" to handle this new customFields on CT which contains the description of the bouquet
      v 1.23.0 : Update reference
      v 1.22.0 : Update reference
      v 1.21.0 : added new order extension methods
      v 1.20.0 : added new settings OutboundOrderShippingAmount and OutboundOrderShippingTaxRate
      v 1.19.0 : addded OrderExtensionMethods.GetSrc
      v 1.18.0 : fix some mistake about updateReadOrder
      v 1.17.5 : FLR(957) order not marked as read by transmitter
      v 1.17.4 : Patch on CustomLineItem.slug format for international orders
      v 1.17.3 : Fix reference
      v 1.17.2 : FLR(964) patch missed phone number
      v 1.17.1 : Patch on OrderService related to the creation of international orders
      v 1.17.0 : Update SharedLib dep to use the latest Kafka consumer fix for new messages
      v 1.16.1 : Fix in query parameters for order history
      v 1.16.0 : Order payment status set to Paid when an order is created
      v 1.15.0 : Added new extension methods for ICartDraft and IAddressDraft, used into OrderService.HandleOrderCreated
      v 1.14.0 : Update reference
      v 1.13.0 : feat(FLR-945) new OrdiniLogHistory MS
      v 1.12.1 : fix query for transmited orders
      v 1.12.0 : feat(FLR-942, FLR-943) react to order accepted/rejected on behalf
      v 1.11.0 : add LocalCountryProductsCategoryKey in CT settings
      v 1.10.0 : feat(FLR-822) react to order assignation removed from legacy to pfs event
      v 1.9.0 : Added order query and order update services
      v 1.8.0 : Update UpdateOrderItem method
      v 1.7.0 : Added CtProductCustomAttributesNames + handle allow_price_different_from_variants
      v 1.6.2 : Fix SharedModel version
      v 1.6.1 : Add CategoryService and some fields in OrderService
      v 1.6.0 : Update reference
      v 1.5.0 : Added IT.SharedLibraries.CT.CustomAttributes.CtOrderCustomAttributesNames
      v 1.4.2 : Patch on order address updated event
      v 1.4.1 : Patch on order address updated event
      v 1.4.0 : Updated objects used in order created, assigned, cancelled, and delivery time updated
      v 1.3.1 : Patch on order rejected event
      v 1.3.0 : Handle order accepted, delivered, rejected
      v 1.2.0 : Update reference
      v 1.1.0 : FLR-910 - All methods moved from IT.Microservices.OrderReactor
      v 1.0.0 : First implementation
    </Description>
    <PackageReleaseNotes>
		v 2.106.0 : Update libs
		v 2.105.0 : Update libs
		v 2.104.0 : Add customer verification email in customerService
		v 2.103.4 : Fix PSM in product comparison
		v 2.103.3 : Add FirstName and LastName in OrderDeliveryDTO
		v 2.103.2 : Add FirstName and LastName in OrderSummaryDTO
		v 2.103.1 : Timestamp cast long instead of int
		v 2.103.0 : Handle Timestamp creationDate
		v 2.102.0 : Rework latitude/longitude getDiff
		v 2.101.0 : Fix little cases for variant check in OrderReactor usage
		v 2.100.0 : Handle FR GFS Order OUT from IRIS for PostOrder
		v 2.99.0 : Update SharedLibs
		v 2.98.0 : Add DeleteCustomer method to CustomerService
		v 2.97.0 : Update sharedLib
		v 2.96.1 : Fix CT customer Query expand issue (others api)
		v 2.96.0 : Fix CT customer Query expand issue
		v 2.95.6 : Handle difference for ExecutingFLoristDeliveryAmount
		v 2.95.5 : Update billing Mobile instead of Phone Ct field
		v 2.95.4 : Fix Convert.ToInt for non numeric for the size
		v 2.95.3 : Added Stem cases for detecting product changes / fix variant key for getDiff
		v 2.95.2 : Fix REAdd DeleteOrderEdits method to OrderService
		v 2.95.1 : Fix product Sync ignore Acc Type for comparison
		v 2.95.0 : Add DeleteOrderEdits method to OrderService
		v 2.94.4 : Add vatNumber, companyNumber and Pec to globalOrder
		v 2.94.3 : Handle custom Field FloristInvoiceUrl and InternalOrderId in getdifference for update
		v 2.94.2 : Handle custom Field FloristInvoicePrinted get and update
		v 2.94.1 : Handle custom Field FloristInvoicePrinted
		v 2.94.0 : Update references
		v 2.93.1 : Fix product sync ignore foreign order update
		v 2.93.0 : Rework product sync for full erase
		v 2.92.1 : Rework expired due date for external price
		v 2.92.0 : Update reference ITF.SharedLibraries for ITF.SharedModels
		v 2.91.1 : Fix mapping executingFloristAmount with fee for productSync + logs
		v 2.91.0 : Add VAT informations in ProductDTO
		v 2.90.1 : Handle OrderCreationDate OrderSync
		v 2.90.0 : Handle SalesOrigin and EveningMoment OrderSync
		v 2.89.0 : Update SharedLibs to have the latest CT lib SDK version to use new CustomerGroupsAssignemtns features / Add UpdateCustomer Method in CustomerService
		v 2.88.4 : Fix Convert RaoProduct convert to LineItem without bundleId info
		v 2.88.3 : Fix getProduct by wrong bundle key in productSync
		v 2.88.2 : Rework single LineItem amount check in productSync
		v 2.88.1 : Remove LineItem amount check in productSync
		v 2.88.0 : Rework Sync Product
		v 2.87.1 : Patch references
		v 2.87.0 : Added executingFloristInvoiceUrl to the IT.SharedLibraries.CT.Orders.IOrderService.HandleOrderAssigned
		v 2.86.0 : handle extra_bundle_infos into order.lineitem (sweden)
		v 2.85.0 : Rework Bundle Extension Method
		v 2.84.0 : Handle Update of the composition field in CT for PSM FR
		v 2.83.6 : Rework mongo Line Item in update part
		v 2.83.5 : Rework total amount lineItem bundle comparison
		v 2.83.4 : Rework bundled CT lineItem Count comparison
		v 2.83.3 : Handle mongo LineItem only in creation part
		v 2.83.2 : Avoid no Bundle Discount LineItem in total lineItem Number Check
		v 2.83.1 : Avoid Discount LineItem creation
		v 2.83.0 : Handle bundle creation in CT
		v 2.82.0 : Add GetByOrderIdAsync for OrderLineItemsPricesRepository and fix some logic to replace instead of insert new line item prices
		v 2.81.0 : fix get diff for recipientTitle with null/empty handling to MRS
		v 2.80.3 : fix ?? in delivery adress comparison
		v 2.80.2 : fix STEM_SIZE constant
		v 2.80.1 : Handle 409 for update CTFields process sync order
		v 2.80.0 : Rework the OrderDifference.DeliveryStreet to handle the StreetNumber duplicate between CT and RAO
		v 2.79.0 : Add DeliveryDistance to OrderSummaryDto and OrderDto for FR needs
		v 2.78.5 : make IUnitOrderService in OrderService as nullable
		v 2.78.4 : product sync consider rem for no bundle case
		v 2.78.3 : product sync consider quantity for amount Check
		v 2.78.2 : order syn getDifferences use fieldComparer
		v 2.78.1 : handle quantity check in matching line item amount check
		v 2.78.0 : Add latitude and longitude for OrderSummaryDto too
		v 2.77.4 : fix typo version
		v 2.77.3 : remove last lib tag
		v 2.77.2 : test last lib tag
		v 2.77.1 : Fix cast to double : Add latitude and longitude extension method for Ct order
		v 2.77.0 : Add latitude and longitude extension method for Ct order
		v 2.76.4 : ProductSync rework update check
		v 2.76.3 : Add logs
		v 2.76.2 : Add constant to get florist preparation time attribute
		v 2.76.1 : Consider FloristFee for register mongoLineItemPrice
		v 2.76.0 : Addded new method to LineItemExtensions
		v 2.75.0 : Addded new method to AddressExtenions
		v 2.74.0 : Product sync consider quantity when compare line Items
		v 2.73.1 : non required ISlackAlertService injection in OrderService constructor
		v 2.73.0 : update lib
		v 2.72.0 : rework Greetings check difference
		v 2.71.0 : Add some logs and error managment for UpdateOrder into CT helping to debug the issue with the order update
		v 2.70.2 : ProductSync fix Recipient Name greetings comparison
		v 2.70.1 : ProductSync handle debundled product on both Rao/CT side
		v 2.70.0 : ProductSync totalAmount check fix
		v 2.69.0 : Reork Moment/window management in OrderService
		v 2.68.0 : Added method to AddressExtensionMethods
		v 2.67.3 : sync bundle inihb pending test
		v 2.67.2 : fix if Greeting is empty in Legacy side we just skip the update into CT (no null value allowed)
		v 2.67.1 : fix number of line item comparison in product difference
		v 2.67.0 : fix nullable orderLineItemsPricesRepository
		v 2.66.0 : adapt lib
		v 2.65.0 : sync bundle inihb pending test
		v 2.64.0 : Fix HandleDeliveryTimeDifference edges cases
		v 2.63.0 : Product Sync iteration 2
		v 2.62.0 : Update ITF.SharedLibraries ref to 8.25.0
		v 2.61.0 : adding context causationId from casted message for helping apm debugging
		v 2.60.0 : Fix HandleOrderAssigned defaultStatus not used
		v 2.59.0 : Fix HandleOrderAssigned variable always null
		v 2.58.0 : Fix publish messages without getting the previous MessageId from the initial message
		v 2.57.0 : First product syn iteration
		v 2.56.2 : Fix Test
		v 2.56.1 : Fix Test
		v 2.56.0 : Add extensions method for product sync
		v 2.55.0 : Rework the IOrderDtoService
		v 2.54.0 : Rework MarketingFee for CT
		v 2.53.1 : Re add default status value to HandleOrderExternalDeliveryFieldsUpdated
		v 2.53.0 : Add executingFloristInvoiceUrl Getter from CT + modifying the OrderDTO to add the new field
		v 2.52.0 : Add executingFloristInvoiceUrl in const for CT Order custom fields
		v 2.51.0 : Added field ExternalDeliveryTrackingCode to OrderSummaryDTO
		v 2.50.0 : Add SetExecutingFloristAmount method to OrderService
		v 2.49.0 : Added extensions methods
		v 2.48.8 : Remove default status value to HandleOrderExternalDeliveryFieldsUpdated
		v 2.48.7 : Update ITF.SharedLibraries reference to 8.17.0
		v 2.48.6 : Change status mapping for AFF to ACCEPTED
		v 2.48.5 : Update ITF.SharedLibraries reference to 8.16.0
		v 2.48.4 : Add default status value to HandleOrderExternalDeliveryFieldsUpdated
		v 2.48.3 : Add limit on delivery date for get to execute orders query
		v 2.48.2 : Force free price product for France when no match price found in CT
		v 2.48.1 : Handle Assign free price product for France
		v 2.48.0 : Fix Default Variant For free price with location
		v 2.47.0 : Handle ProductVariant expired Price with Roses a la tige case
		v 2.46.0 : Handle ProductVariant expired Price
		v 2.45.0 : Added methods to VariantExtensionMethods
		v 2.44.3 : Add HandleDeliveryTimes test method
		v 2.44.2 : Add GetToExecuteOrders test method
		v 2.44.1 : Add BuilderMessageConverter
		v 2.44.0 : Add GetToExecuteOrders test method
		v 2.43.2 : Fixed Evening getting removed from CT
		v 2.43.1 : Fixed the externalDeliveryTrackingCode parameter check in the UpdateOrderExternalDeliveryFields method
		v 2.43.0 : Added ExternalDeliveryTrackingCode to ExternalDeliveryDTO + extension method
		v 2.42.0 : Updated OrderService.HandleOrderExternalDeliveryFieldsUpdated
		v 2.41.0 : Remove delivery type from difference for order update
		v 2.40.0 : Added PaymentExtensionMethods
		v 2.39.1 : Add sort by internal order ID for GetToExecuteOrders - code fix
		v 2.39.0 : Add sort by internal order ID for GetToExecuteOrders
		v 2.38.0 : Update ITF.SharedModels ref to 8.9.0
		v 2.37.0 : Refacto GetDifferenceForOrderUpdate
		v 2.36.1 : GSEMIG-214 - Patch
		v 2.36.0 : GSEMIG-214 - Update ITF.SharedLibraries ref to 8.13.1
		v 2.35.0 : Added methods to VariantExtensions
		v 2.34.0 : FLR-1686 Update FloristStatus Ct to DELIVERED when UpdateStatusMessage bring DELIVERED deliveryStatus
		v 2.33.0 : Fix wrong fields added in PostOrderCT reacted to OrderPlaced/UpdatedMessage
		v 2.32.0 : Update Libs
		v 2.31.0 : Added methods to OrderExtensionMethods
		v 2.30.0 : Add a method to search duplicated orders
		v 2.29.0 : Update ITF.ShareLibraries reference to 8.11.0
		v 2.28.0 : Added methods to CustomLineItemExtensionMethods
		v 2.27.0 : Update RAO fields in CT
		v 2.26.1 : Update ITF.SharedModels reference to 8.4.1
		v 2.26.0 : Inhib Product Update CT through Messages
		v 2.25.0 : Added methods to AddressExtensionMethods
		v 2.24.1 : Patch VariantExtensions.GetBundleVariants
		v 2.24.0 : Added IOrderService.HandleOrderItemExecutorAmountUpdated
		v 2.23.0 : Update ITF.SharedModels reference to 8.3.0
		v 2.22.0 : Update GetDifferenceForUpdate Methode to handle PFS mapping update message trigger
		v 2.21.0 : Update Libs
		v 2.20.0 : Update Libs
		v 2.19.0 : Update ITF.SharedLibraries reference to 8.8.0
		v 2.18.0 : Added methods to VariantExtensions - GetProductClassification
		v 2.17.0 : Update ITF.SharedLibraries reference to 8.7.0
		v 2.16.0 : Added methods to VariantExtensions - GetProductType
		v 2.15.0 : Added methods to VariantExtensions - GetBundleVariantsObjectId
		v 2.14.0 : Replace delivery country code UK with GB into OrderService.HandleOrderCreated
		v 2.13.0 : Added methods to VariantExtensions
		v 2.12.1 : Added PaymentService - fix
		v 2.12.0 : Added PaymentService
		v 2.11.0 : Removed methods into VariantExtensions for accessing custom attribute available-colors
		v 2.10.0 : Replace methods to VariantExtensions to use attr(Label) instead of attr(VariantName)
		v 2.9.1  : Fix method to Update external delivery Fields for order CT
		v 2.9.0  : Added methods to Update external delivery Fields for order CT
		v 2.7.0  : Update ITF.SharedLibraries ref to 8.1.0
		v 2.6.0  : Added property BundleKey to IT.SharedLibraries.CT.ExtensionMethods.Domain.BundleVariant
		v 2.5.0  : Added methods to ProductExtensions
		v 2.4.0  : FLR-1581
		v 2.3.0 : Added methods to VariantExtensions
		v 2.2.0 : Added GetCartDiscountById to DiscountCodeService
		v 2.1.0  : Updated IT.SharedLibraries.CT.Orders.Services.IOrderDtoBuilderService and IOrderSummaryDtoBuilderService
		v 2.0.3 : FLR-1560 patch 2
		v 2.0.2  : Update reference ITF.SharedModels to 8.0.2
		v 2.0.1  : update libs
		v 2.0.0 : Update to .NET 8
		v 1.94.0 : DIGI-762 Added the common discount code processes
		v 1.93.3 : FLR-1560 patch
		v 1.93.2 : Enriched CustomObjectService + VariantExtensions + ProductService
		v 1.93.1 : Enriched CustomObjectService + VariantExtensions
		v 1.93.0 : Added CustomObjectService
		v 1.92.0 : Added methods to VariantExtensions
		v 1.91.1 : Patched GlobalOrderItemUpdated event management
		v 1.91.0 : Handle orderCreatedMessage that not contain invoiceFirstName/LastName
		v 1.90.1 : Re-throw Concurrent Exception (409) on order update
		v 1.90.0 : Added methods to LineItemExtensions
		v 1.89.0 : FLR-1560 [Spain and PT] Add 9€ of Marketing fees for GFS out order
		v 1.88.0 : Added methods to VariantExtensions
		v 1.87.0 : Added methods to VariantExtensions
		v 1.86.2 : fix typo
		v 1.86.1 : adapt address field to remove additionnalStreetInfo concatenation
		v 1.86.0 : add additionnalStreetInfo and companyName fields in delivertDto for order
		v 1.85.1 : fix typo
		v 1.85.0 : handle companyName fields shipping globalOrderModel
		v 1.84.0 : handle additionnalStreetInfo, invoiceFirstName, invoiceLastName and companyName fields shipping and billing globalOrderModel
		v 1.83.0 : Added new extension methods
		v 1.82.0 : Added methods to VariantExtensions
		v 1.81.0 : Added methods to VariantExtensions
		v 1.80.2 : Return zero with GetDeliveryPrice extension method in case of null value. External delivery informations rename
		v 1.80.1 : Patch VariantExtensions.SetSummary
		v 1.80.0 : Added methods to VariantExtensions
		v 1.79.0 : Added methods to VariantExtensions
		v 1.78.0 : Added methods to VariantExtensions
		v 1.77.0 : Added methods to Product and Category services
		v 1.76.0 : Added CT Variants extensions methods
		v 1.75.0 : Add Order reactor RAO FR messages handling for status update for CT Order
		v 1.74.0 : Added TaxCategoryService + extensions methods
		v 1.73.0 : Added new methods to IT.SharedLibraries.CT.ExtensionMethods.VariantExtensionMethods
		v 1.72.1 : add more delivery fields in orderSummaryDto
		v 1.72.0 : add delivery fields in orderSummaryDto
		v 1.71.0 : FLR-1497 Handle street Number, AdditionalStreetInfo and Province in billing address
		v 1.70.0 : Update references ITF.SharedLibraries to 7.44.2 and ITF.SharedModels to 7.67.0
		v 1.69.0 : Add contact first name and last name in order delivery DTO
		v 1.68.0 : Added new Ct extensions methods
		v 1.67.0 : Added IT.SharedLibraries.CT.ExtensionMethods.AddressExtensionMethods.GetContactTitle
		v 1.66.0 : update deps -&gt; so new CT SDK Version / adapt some little changes because of CT Update / Begin to add RAO FR Messages handling to update CT (FR MIG)
      v 1.65.0 : Add Messages and models for deliveryStatus and deliveryCost
      v 1.64.0 : Use shared Lib 7.33.0
      v 1.63.0 : FLR-1411 Handle florist_product_composition per country
      v 1.62.0 : FLR-1375 Handle OrderRecipientCoordinatesUpdated
      v 1.61.0 : Handle line Item composition for specific product
      v 1.60.1 : Move Region field in DeliveryDto
      v 1.60.0 : Add Region field in orderDto
      v 1.59.1 : Patch on IT.SharedLibraries.CT.Orders.OrderService.HandleOrderAssigned
      v 1.59.0 : FLR-847 Added IT.SharedLibraries.CT.Orders.Services.GetNationalShippingMethodKey
      v 1.58.0 : FLR-847 Update reference ITF.SharedModels to 7.59.1
      v 1.57.0 : Update reference
      v 1.56.0 : Added IOrderQueryService.GetOrderById
      v 1.55.0 : Added class AddressExtensionMethods
      v 1.54.0 : Fix confusion between Shipping Description and Billing Description
      v 1.53.0 : Added methods to IT.SharedLibraries.CT.ExtensionMethods.CustomLineItemExtensionMethods
      v 1.52.0 : FLR-1279: Hide unnecessary products from AllProduct MS + FLR-1277 Reset the Delivery in progress field when an order is assigned
      v 1.51.2 : minor fixes
      v 1.51.1 : handle more behavior about LimitAcceptedBefore Date
      v 1.51.0 : Fix get LimitAcceptedBefore Date
      v 1.50.0 : FLR-1250 Use the product amount for the executor when returning an order to PFS
      v 1.49.0 : FLR-1249 Add a new field into the Assignation Order Events to get the product amount for the executor
      v 1.48.0 : Handle the lack of custom fields in getDeliveryDate method
      v 1.47.0 : FLR-1427 Handle 9.99€ and 10.99€ delivery cost for Spain
      v 1.46.0 : Add itemNumber on orderDto
      v 1.45.0 : Add isModified bool on order in CT
      v 1.44.2 : Fix on UpdateFloristOrderStatus: remove the value of the executor florist when removal_assignment is called
      v 1.44.1 : Improved error log in IT.SharedLibraries.CT.Orders.OrderService.GetById
      v 1.44.0 : Updated OrderDTO, added ExecutingFlorist and TransmitterFlorist
      v 1.43.1 : Patch on HandleOrderCreated: set empty card message when it is null
      v 1.43.0 : FLR-1173 Added method IT.SharedLibraries.CT.ShippingMethods.IShippingMethodService.UpdateSetFixedShippingRate
      v 1.42.0 : Added method IT.SharedLibraries.CT.ShippingMethods.GetInternationalDeliveryFeeForPfs()
      v 1.41.0 : Delivery fee for international outbound order read from CT shipping method
      v 1.40.1 : Merge constant files
      v 1.40.0 : Add GetCustomString method to retrieve custom fields value
      v 1.39.0 : Add GetProductWithCategoriesById to get unpublished products (get IProduct not IProductProjection)
      v 1.38.0 : Update reference
      v 1.37.1 : FLR-1141 Order create event: handle equal products sent by legacy
      v 1.37.0 : Update reference
      v 1.36.3 : Fix assignation removal
      v 1.36.2 : Fix version
      v 1.36.1 : Fix references
      v 1.36.0 : Handle GlobalOrderModel.ExecutingFloristType
      v 1.35.2 : Added variants extensions methods
      v 1.35.1 : Fix method for OrderItemUpdated
      v 1.35.0 : Added variants extensions methods
      v 1.34.0 : FLR-1127 Handle accessory products for non Flower and Gifts executor use case
      v 1.33.1 : Fix version
      v 1.33.0 : Added IOrderDtoBuilderService and IOrderSummaryDtoBuilderService + ct extension methods
      v 1.32.1 : Fix use of shipping.comments instead of notes in global order model field
      v 1.32.0 : Add limit parameter to GetToExecuteOrders query
      v 1.31.0 : Added IT.SharedLibraries.CT.IShippingMethodService.GetDeliveryFeeForPfs
      v 1.30.0 : Use pfs specific shipping method
      v 1.29.1 : Updated method signatures for OrderService.GetCustomLineItemForForeignOrder and OrderService.GetLineItemForLocalOrder + added CtCustomException
      v 1.29.0 : Order Create: when the product allow price different from variants, mkfee are taken from the request, otherwise the value stored into the CT product is used
      v 1.28.7 : Added log info in case ExternalPrice for line item is setted
      v 1.28.6 : Patch on HandleOrderCreated on the evaluation of CT product variant
      v 1.28.5 : Patch on fetching line item - v2
      v 1.28.4 : Patch on fetching line item
      v 1.28.3 : Patch on marketingfee for national order
      v 1.28.2 : Handle marketingfee for national order + log shipping method
      v 1.28.1 : Use extentions methods for custom fields
      v 1.28.0 : Use more fields on OrderService
      v 1.27.2 : Patch on HandlerOrderCreated
      v 1.27.1 : Fix reference
      v 1.27.0 : handle order recipient name and phone number update
      v 1.26.1 : Fix DeliveryCountrycode
      v 1.26.0 : Set "PFS" as default source of order if source is empty. Update shared model version.
      v 1.25.0 : Update reference
      v 1.24.0 : added new constant "composition" to handle this new customFields on CT which contains the description of the bouquet
      v 1.23.0 : Update reference
      v 1.22.0 : Update reference
      v 1.21.0 : added new order extension methods
      v 1.20.0 : added new settings OutboundOrderShippingAmount and OutboundOrderShippingTaxRate
      v 1.19.0 : addded OrderExtensionMethods.GetSrc
      v 1.18.0 : fix some mistake about updateReadOrder
      v 1.17.5 : FLR(957) order not marked as read by transmitter
      v 1.17.4 : Patch on CustomLineItem.slug format for international orders
      v 1.17.3 : Fix reference
      v 1.17.2 : FLR(964) patch missed phone number
      v 1.17.1 : Patch on OrderService related to the creation of international orders
      v 1.17.0 : Update SharedLib dep to use the latest Kafka consumer fix for new messages
      v 1.16.1 : Fix in query parameters for order history
      v 1.16.0 : Order payment status set to Paid when an order is created
      v 1.15.0 : Added new extension methods for ICartDraft and IAddressDraft, used into OrderService.HandleOrderCreated
      v 1.14.0 : Update reference
      v 1.13.0 : feat(FLR-945) new OrdiniLogHistory MS
      v 1.12.1 : fix query for transmited orders
      v 1.12.0 : feat(FLR-942, FLR-943) react to order accepted/rejected on behalf
      v 1.11.1 : fix query for transmited orders
      v 1.11.0 : add LocalCountryProductsCategoryKey in CT settings
      v 1.10.0 : feat(FLR-822) react to order assignation removed from legacy to pfs event
      v 1.9.0 : Added order query and order update services
      v 1.8.0 : Update UpdateOrderItem method
      v 1.7.0 : Added CtProductCustomAttributesNames + handle allow_price_different_from_variants
      v 1.6.2 : Fix SharedModel version
      v 1.6.1 : Add CategoryService and some fields in OrderService
      v 1.6.0 : Update reference
      v 1.5.0 : Added IT.SharedLibraries.CT.CustomAttributes.CtOrderCustomAttributesNames
      v 1.4.2 : Patch on order address updated event
      v 1.4.1 : Patch on order address updated event
      v 1.4.0 : Updated objects used in order created, assigned, cancelled, and delivery time updated
      v 1.3.1 : Patch on order rejected event
      v 1.3.0 : Handle order accepted, delivered, rejected
      v 1.2.0 : Update reference
      v 1.1.0 : FLR-910 - All methods moved from IT.Microservices.OrderReactor
      v 1.0.0 : First implementation
    </PackageReleaseNotes>
    <PackageTags>Git on Azure Devops</PackageTags>
    <RepositoryUrl>The full git URL</RepositoryUrl>
    <Copyright>Interflora France</Copyright>
    <Version>2.106.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="ITF.SharedLibraries" Version="8.51.0" />
    <PackageReference Include="ITF.SharedModels" Version="8.52.0" />
    <PackageReference Include="ObjectsComparer" Version="1.4.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\ITF.SharedLibraries\src\ITF.SharedLibraries.csproj" />
    <ProjectReference Include="..\..\ITF.SharedModels\src\ITF.SharedModels.csproj" />
  </ItemGroup>
</Project>