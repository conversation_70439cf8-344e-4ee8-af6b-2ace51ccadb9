﻿using ITF.Lib.Common.Notifications.Messages;

namespace ITF.SharedModels.Messages.Group.Customer;

public static partial class Messages
{
    public static partial class V1
    {
        public class CreatedAccountRequestedMessage : BaseMessage<CreatedAccountRequestedPayload>, IMessageKey
        {
            public string GetMessageKey()
                => Payload?.CustomerEmail ?? "";
        }

        public class CreatedAccountRequestedPayload : IPayload
        {
            public string CustomerEmail { get; set; } = string.Empty;
            public string CustomerFirstName { get; set; } = string.Empty;
            public string CustomerLastName { get; set; } = string.Empty;
            public string RedirectUrl { get; set; } = string.Empty;
            public string EventID { get; set; } = Guid.NewGuid().ToString();
            public DateTime EventDate { get; set; } = DateTime.Now;
        }
    }
}


