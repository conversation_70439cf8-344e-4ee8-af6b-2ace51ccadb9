namespace IT.Microservices.AuthenticationApi.ResetPassword;

public record AskResetPasswordRequest(string Email, string RedirectUrl);

public record AskResetPasswordResponse(string RedirectUrlWithKey);

public record ResetPasswordRequest(string Key, string NewPassword);

public record ResetPasswordResponse(bool Success);

public record ValidateResetKeyRequest(string Key);

public record ValidateResetKeyResponse(bool IsValid);

public record ResetPasswordFailedResponse(string? error = null, string? error_description = null, [property: JsonIgnore] bool? isException = false);

public class ResetPasswordController(ILogger<ResetPasswordController> logger, IResetPasswordUseCase resetPasswordUseCase) : BaseController
{
    [SwaggerOperation(
        Summary = "Ask for password reset",
        Description = "Validates user existence and generates a secure reset key with redirect URL",
        OperationId = "AskResetPassword")]
    [SwaggerResponse(200, "Reset key generated successfully", typeof(AskResetPasswordResponse))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occurred, please contact us")]
    [HttpPost("ask")]
    public async Task<IActionResult> AskResetPasswordAsync([FromBody] AskResetPasswordRequest req)
    {
        if (req is null)
            return BadRequest(new Error("InvalidRequestData", "Request body is null or invalid"));

        logger.LogWarning("Request ask reset password received : {req}", req.Serialize());
        var res = await resetPasswordUseCase.ProcessAskResetPassword(req);
        return Ok(res.IsSuccess ? res.Value : res.Error);
    }

    [SwaggerOperation(
        Summary = "Validate reset key",
        Description = "Validates if a reset key is valid and not expired without resetting the password",
        OperationId = "ValidateResetKey")]
    [SwaggerResponse(200, "Key validation result", typeof(ValidateResetKeyResponse))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occurred, please contact us")]
    [HttpPost("validate")]
    public async Task<IActionResult> ValidateResetKeyAsync([FromBody] ValidateResetKeyRequest req)
    {
        if (req is null)
            return BadRequest(new Error("InvalidRequestData", "Request body is null or invalid"));

        logger.LogWarning("Request validate reset key received : {Req}", (req with { Key = "*******" }).Serialize());
        var res = await resetPasswordUseCase.ProcessValidateResetKey(req);
        return Ok(res.IsSuccess ? res.Value : res.Error);
    }

    [SwaggerOperation(
        Summary = "Reset password with key",
        Description = "Validates the reset key and updates the user's password",
        OperationId = "ResetPassword")]
    [SwaggerResponse(200, "Password reset successfully", typeof(ResetPasswordResponse))]
    [SwaggerResponse(400, "A parameter is invalid", typeof(Error))]
    [SwaggerResponse(500, "An unknown error occurred, please contact us")]
    [HttpPost("reset")]
    public async Task<IActionResult> ResetPasswordAsync([FromBody] ResetPasswordRequest req)
    {
        if (req is null)
            return BadRequest(new Error("InvalidRequestData", "Request body is null or invalid"));

        logger.LogWarning("Request reset password received : {Req}", (req with { Key = "*******", NewPassword = "*******" }).Serialize());
        var res = await resetPasswordUseCase.ProcessResetPassword(req);
        return Ok(res.IsSuccess ? res.Value : res.Error);
    }
}
