﻿using ITF.Lib.Common.Notifications.Messages;
using ITF.SharedModels.Group.Enums.Gfs;
using ITF.SharedModels.Messages.GFS.Message;
using ITF.SharedModels.Messages.Italy;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace ITF.SharedModels.Messages.Group.Gfs
{

    public static partial class Messages
    {
        public static partial class V1
        {
            public class GroupEDACancelOrderMessage : BaseMessage<GroupEDACancelOrderMessagePayload>, IMessageKey, IDistributedTracing
            {
                public string GetMessageKey() => Guid.NewGuid().ToString();

                public void SetDistributedTracingData(string distributedTracingData)
                {
                    DistributedTracingData = distributedTracingData;
                }

                public GroupEDACancelOrderMessage()
                {
                    this.Payload = new GroupEDACancelOrderMessagePayload();
                }

                public static implicit operator GroupEDACancelOrderMessage(GfsEDACancelOrderMessageContainer v)
                {
                    GroupEDACancelOrderMessage msg = new();
                    msg.Payload = new();
                    msg.Payload.Reason = v.CancelOrderMessage.Reason;
                    msg.Payload.Text = v.CancelOrderMessage.Text;
                    msg.Payload.ClosingInfo = v.CancelOrderMessage.ClosingInfo;
                    msg.Payload.BackchargeToUnitAmount = v.CancelOrderMessage.BackchargeToUnitAmount;
                    msg.Payload.Id = v.CancelOrderMessage.Id;
                    msg.Payload.GFSgateNumber = v.CancelOrderMessage.GFSgateNumber;
                    msg.Payload.MessageStatus = v.CancelOrderMessage.MessageStatus;
                    msg.Payload.MessageType = v.CancelOrderMessage.MessageType;
                    msg.Payload.CreatedDate = v.CancelOrderMessage.CreatedDate;
                    msg.Payload.ModifiedDate = v.CancelOrderMessage.ModifiedDate;
                    msg.Payload.ConfirmedDate = v.CancelOrderMessage.ConfirmedDate;
                    msg.Payload.FromUnitID = v.CancelOrderMessage.FromUnitID;
                    msg.Payload.FromUnitMessageID = v.CancelOrderMessage.FromUnitMessageID;
                    msg.Payload.ToUnitID = v.CancelOrderMessage.ToUnitID;
                    msg.Payload.ToUnitMessageID = v.CancelOrderMessage.ToUnitMessageID;
                    msg.Payload.Operator = v.CancelOrderMessage.Operator;
                    msg.Payload.Priority = v.CancelOrderMessage.Priority;
                    msg.Payload.RelatedMessageId = v.CancelOrderMessage.RelatedMessageId;
                    msg.Payload.Read = v.CancelOrderMessage.Read;
                    return msg;
                }
            }
        }
    }


    public class GroupEDACancelOrderMessagePayload : LegacyPayload, IEquatable<GroupEDACancelOrderMessagePayload>
    {
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsCancelOrderMessageReason Reason { get; set; }
        public string Text { get; set; } = default!;
        public bool ClosingInfo { get; set; }
        public int BackchargeToUnitAmount { get; set; }
        public int Id { get; set; }
        public int GFSgateNumber { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageStatus MessageStatus { get; set; }
        [JsonConverter(typeof(StringEnumConverter))]
        public GfsMessageType MessageType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime ModifiedDate { get; set; }
        public DateTime? ConfirmedDate { get; set; }
        public int FromUnitID { get; set; }
        public int FromUnitMessageID { get; set; }
        public int ToUnitID { get; set; }
        public int ToUnitMessageID { get; set; }
        public string Operator { get; set; } = default!;
        public int Priority { get; set; }
        public int? RelatedMessageId { get; set; }
        public bool Read { get; set; }

        public bool Equals(GroupEDACancelOrderMessagePayload parameter)
        {
            return (Reason == parameter.Reason &&
                    Text == parameter.Text &&
                    ClosingInfo == parameter.ClosingInfo &&
                    BackchargeToUnitAmount == parameter.BackchargeToUnitAmount &&
                    Id == parameter.Id &&
                    GFSgateNumber == parameter.GFSgateNumber &&
                    MessageStatus == parameter.MessageStatus &&
                    MessageType == parameter.MessageType &&
                    CreatedDate == parameter.CreatedDate &&
                    ModifiedDate == parameter.ModifiedDate &&
                    ConfirmedDate == parameter.ConfirmedDate &&
                    FromUnitID == parameter.FromUnitID &&
                    FromUnitMessageID == parameter.FromUnitMessageID &&
                    ToUnitID == parameter.ToUnitID &&
                    ToUnitMessageID == parameter.ToUnitMessageID &&
                    Operator == parameter.Operator &&
                    Priority == parameter.Priority &&
                    RelatedMessageId == parameter.RelatedMessageId &&
                    Read == parameter.Read
                    );
        }
        public override bool Equals(object obj)
        {
            return Equals(obj as GroupEDACancelOrderMessagePayload);
        }

        public override int GetHashCode() => new
        {
            Reason,
            Text,
            ClosingInfo,
            BackchargeToUnitAmount,
            Id,
            GFSgateNumber,
            MessageStatus,
            MessageType,
            CreatedDate,
            ModifiedDate,
            ConfirmedDate,
            FromUnitID,
            FromUnitMessageID,
            ToUnitID,
            ToUnitMessageID,
            Operator,
            Priority,
            RelatedMessageId,
            Read
        }.GetHashCode();
    }
}
