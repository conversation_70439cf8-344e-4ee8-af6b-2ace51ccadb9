﻿using commercetools.Sdk.Api.Models.Orders;
using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IT.SharedLibraries.CT.Orders.Comparers;

public class FieldComparer : IEqualityComparer<string>
{
    public bool Equals(string? incomingField, string? currentField)
    {
        if (string.IsNullOrWhiteSpace(incomingField) && string.IsNullOrWhiteSpace(currentField)) return true;
        if (string.IsNullOrWhiteSpace(incomingField) || string.IsNullOrWhiteSpace(currentField)) return false; 

        return incomingField.Trim().Equals(currentField.Trim(), StringComparison.CurrentCultureIgnoreCase);
    }

    public int GetHashCode([DisallowNull] string obj) => obj.GetHashCode();



    public bool Equals(double? amount, decimal? moneyAmount)
    {
        if (!amount.HasValue && !moneyAmount.HasValue) return true;
        if (!amount.HasValue || !moneyAmount.HasValue) return false;

        return (decimal)amount.Value == moneyAmount.Value;
    }



}
