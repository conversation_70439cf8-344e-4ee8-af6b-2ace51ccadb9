﻿using System;

namespace IT.SharedLibraries.CT.Orders.DTO
{
    public class OrderSummaryDTO : OrderDTOBase
    {
        public DateTime DeliveryDate { get; set; }
        public string DeliveryMoment { get; set; }
        public string DeliveryTime { get; set; }
        public string ExternalDeliveryService { get; set; }
        public bool ExternalDeliveryServiceRequested { get; set; }
        public DateTime? ExternalDeliveryScheduledDate { get; set; }
        public string ExternalDeliveryStatus { get; set; }
        public decimal ExternalDeliveryCost { get; set; }
        public string? ExternalDeliveryTrackingCode { get; set; }
        public string DeliveryPhone { get; set; }
        public string DeliveryStreet { get; set; }
        public string DeliveryProvince { get; set; }
        public string DeliveryRegion { get; set; }
        public decimal ExecutingFloristDeliveryAmount { get; set; }
        public string ProductImage { get; set; }
        public string ProductCode { get; set; }
        public string Town { get; set; }
        public string Name { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Invoice { get; set; }
        public string PostalCode { get; set; }
        public bool Checked { get; set; }
        public string ExecutingFloristId { get; set; }
        public string FloristOrderStatus { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public double? DeliveryDistance { get; set; }
    }
}
